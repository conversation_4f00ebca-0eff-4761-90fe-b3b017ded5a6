{"name": "ryvyl-dashboard", "version": "1.0.0", "private": true, "author": "Encorp.io", "license": "", "description": "", "bugs": {"url": "https://github.com/creativetimofficial/ct-material-dashboard-pro-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/ct-material-dashboard-pro-react.git"}, "dependencies": {"@emotion/cache": "11.10.8", "@emotion/react": "11.10.8", "@emotion/styled": "11.10.8", "@fullcalendar/core": "^6.1.6", "@fullcalendar/daygrid": "6.1.6", "@fullcalendar/interaction": "6.1.6", "@fullcalendar/react": "6.1.6", "@fullcalendar/timegrid": "6.1.6", "@mui/icons-material": "^5.15.21", "@mui/material": "^5.15.21", "@mui/x-date-pickers": "^7.8.0", "@react-jvectormap/core": "1.0.4", "@react-jvectormap/world": "1.1.2", "@tanstack/react-table": "^8.17.3", "axios": "^1.6.7", "chart.js": "4.3.0", "chroma-js": "2.4.2", "dayjs": "^1.11.11", "draft-convert": "^2.1.13", "draft-js": "^0.11.7", "dropzone": "6.0.0-beta.2", "flatpickr": "4.6.13", "formik": "2.2.9", "html-react-parser": "3.0.16", "html2canvas": "^1.4.1", "json-2-csv": "^5.5.2", "jwt-decode": "^4.0.0", "prop-types": "15.8.1", "react": "^18.2.0", "react-chartjs-2": "5.2.0", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-flatpickr": "3.10.13", "react-github-btn": "1.4.0", "react-qr-code": "^2.0.15", "react-router-dom": "6.11.0", "react-scripts": "^5.0.1", "react-table": "^7.8.0", "react-toastify": "^10.0.5", "stylis": "4.1.4", "stylis-plugin-rtl": "2.1.1", "uuid": "9.0.0", "uuidv4": "^6.2.13", "yup": "1.1.1"}, "scripts": {"start": "GENERATE_SOURCEMAP=true react-scripts start", "start-staging": "PORT=3001 GENERATE_SOURCEMAP=false react-scripts start", "start-production": "PORT=4001 GENERATE_SOURCEMAP=false react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "winBuild": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "winStart": "set \"GENERATE_SOURCEMAP=false\" && react-scripts start", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "prepare": "husky", "format": "pretty-quick --staged"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.57.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "4.6.0", "husky": "^9.0.11", "prettier": "^3.0.0", "pretty-quick": "^4.0.0"}, "overrides": {"svgo": "3.0.2"}}