import { jwtDecode } from 'jwt-decode';

export function checkAuthorization(action, object) {
  const token = localStorage.getItem('token');
  let isAuthenticated = true;
  if (token) {
    const decoded = jwtDecode(token);

    if (action && object && decoded) {
      const permission = decoded.permissions?.length > 0 ? decoded.permissions : [];
      isAuthenticated = permission.some(
        (item) =>
          (item.action === action || item.action === 'manage') && (item.object === object || item.object == 'all')
      );
    }
  }

  return isAuthenticated;
}
