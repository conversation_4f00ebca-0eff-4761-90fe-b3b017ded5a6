import { toast } from 'react-toastify';
import { updatePassword } from 'requests/user';

export async function changePassword(e, formData, toastId, currentUserId, setFormData) {
  e.preventDefault();
  if (formData.password !== formData.confirmPassword) {
    toast.error('Passwords must match', { className: 'toast-width-600' });
    return;
  }
  toastId.current = toast.loading('Updating password...');
  await updatePassword(formData, currentUserId)
    .then(() => {
      toast.update(toastId.current, {
        type: 'success',
        render: 'Password updated successfully',
        autoClose: 2000,
        isLoading: false,
        className: 'toast-width-600'
      });
    })
    .catch((error) => {
      toast.update(toastId.current, {
        type: 'error',
        render: error.message,
        autoClose: 3000,
        isLoading: false,
        className: 'toast-width-600'
      });
    })
    .finally(() => {
      setFormData({
        oldPassword: '',
        password: '',
        confirmPassword: ''
      });
    });
}
