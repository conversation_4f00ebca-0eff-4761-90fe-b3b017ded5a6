import { useEffect, useMemo, useState, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';

import { useReactTable, getCoreRowModel, getFilteredRowModel, flexRender } from '@tanstack/react-table';

import { useAsyncDebounce } from 'react-table';

// @mui material components
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableRow from '@mui/material/TableRow';
import Icon from '@mui/material/Icon';
import Autocomplete from '@mui/material/Autocomplete';

// Material Dashboard 2 PRO React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDInput from 'components/MDInput';
import MDPagination from 'components/MDPagination';

// Material Dashboard 2 PRO React examples
import DataTableHeadCell from 'examples/Tables/DataTable/components/DataTableHeadCell';
import DataTableBodyCell from 'examples/Tables/DataTable/components/DataTableBodyCell';
import { Badge, Divider, IconButton } from '@mui/material';
import { Refresh } from '@mui/icons-material';
import { fetchTableData } from 'helpers/fetchTableData';
import { useAsyncValue, useSearchParams } from 'react-router-dom';
import MDBadge from 'components/MDBadge';
import PaginationButtons from './components/PaginationButtons';
import TableToolbar from './components/TableToolbar';

function DataTable({
  entriesPerPage = { defaultValue: 10, entries: [] },
  canSearch = false,
  showTotalEntries = true,
  tableTheme = { variant: 'gradient', color: 'info' },
  noEndBorder = false,
  sorting = [],
  setSorting = () => {},
  table,
  transformData,
  fetchingFunction,
  refreshData,
  filters,
  submitHandler,
  filtersComponent,
  additionalData,
  dynamicColumns
}) {
  const [columns, setColumns] = useState(table.columns);
  const [data, setData] = useState(table.rows);
  const [entriesStart, setEntriesStart] = useState(0);
  const [entriesEnd, setEntriesEnd] = useState(0);
  const [allEntity, setAllEntity] = useState(0);
  const [refreshHandler, setRefreshHandler] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const currentPage = searchParams.get('page') ? parseInt(searchParams.get('page')) - 1 : 0;
  const isFirstRenderOnSorting = useRef(true);

  const [pagination, setPagination] = useState({
    pageIndex: currentPage, //initial page index
    pageSize: entriesPerPage.defaultValue ? entriesPerPage.defaultValue : 10 //default page size
  });

  // Helper function to get filters from URL parameters
  const getFiltersFromURL = () => {
    const urlFilters = {};

    const name = searchParams.get('name');
    const registrationNumber = searchParams.get('registrationNumber');

    if (name) urlFilters.name = name;
    if (registrationNumber) urlFilters.registrationNumber = registrationNumber;

    const mastercardMatchStatuses = searchParams.get('mastercardMatchStatuses');
    const visaMatchStatuses = searchParams.get('visaMatchStatuses');
    const sumsubAnswers = searchParams.get('sumsubAnswers');
    const sumsubStatuses = searchParams.get('sumsubStatuses');

    if (mastercardMatchStatuses) {
      urlFilters.mastercardMatchStatuses = mastercardMatchStatuses.split(',');
    }
    if (visaMatchStatuses) {
      urlFilters.visaMatchStatuses = visaMatchStatuses.split(',');
    }
    if (sumsubAnswers) {
      urlFilters.sumsubAnswers = sumsubAnswers.split(',');
    }
    if (sumsubStatuses) {
      urlFilters.sumsubStatuses = sumsubStatuses.split(',');
    }

    return urlFilters;
  };

  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set('page', (pagination.pageIndex + 1).toString());
    setSearchParams(params);
  }, [pagination.pageIndex, setSearchParams]);

  const debouncedFetch = useCallback(
    useAsyncDebounce(() => {
      if (fetchingFunction) {
        fetchData();
      }
    }, 300), // 300ms delay
    [fetchData, fetchingFunction]
  );

  useEffect(() => {
    debouncedFetch();
  }, [table, pagination, refreshHandler, refreshData, submitHandler, filters]);

  useEffect(() => {
    if (isFirstRenderOnSorting.current) {
      isFirstRenderOnSorting.current = false;
      return; // Skip the first run
    }

    setPagination((prev) => {
      if (prev.pageIndex !== 0) {
        return { ...prev, pageIndex: 0 };
      }

      return prev;
    });
  }, [sorting]);

  function setAllData(data) {
    let newArray = [];
    if (additionalData) {
      newArray = data.map((obj) => ({
        ...obj,
        additionalData: additionalData
      }));

      return setData(newArray);
    }
    setData(data);
  }

  function fetchData() {
    fetchTableData({
      table,
      fetchingFunction,
      transformData,
      limit: pagination.pageSize,
      page: pagination.pageIndex,
      defaultColumns: table.columns,
      filters: filters,
      sorting: sorting,
      dynamicColumns: dynamicColumns
    }).then((res) => {
      setAllData(res.rows);
      setEntriesStart(res.pagingCounter);
      setEntriesEnd(Number(res.limit) * Number(res.page));
      setAllEntity(res.totalDocs);
      setColumns(res.columns);
    });
  }

  const tableInstance = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: setPagination,
    manualSorting: true,
    onSortingChange: setSorting,
    rowCount: allEntity,
    manualPagination: true,

    state: {
      pagination,
      pageIndex: pagination.pageIndex,
      sorting
    }
  });
  const {
    getHeaderGroups,
    prepareRow,
    getRowModel,
    getPageOptions,
    getCanPreviousPage,
    getCanNextPage,
    setPageIndex,
    nextPage,
    previousPage,
    setPageSize,
    setGlobalFilter
  } = tableInstance;

  const rows = getRowModel().rows;
  const pageOptions = getPageOptions();
  const {
    pageIndex,
    globalFilter,
    pagination: { pageSize }
  } = tableInstance.getState();

  // Set the entries per page value based on the select value
  const setEntriesPerPage = (value) => setPageSize(value);

  // Search input value state
  const [search, setSearch] = useState(globalFilter);

  // Search input state handle
  const onSearchChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 100);

  const sortColumn = (header) => {
    const columnId = header.column.id;

    if (sorting.length == 0) {
      header.column.toggleSorting(false, false);
    } else if (sorting[0].id == columnId) {
      if (sorting[0].desc == false) {
        header.column.toggleSorting(true, false);
      } else {
        header.column.clearSorting();
      }
    } else {
      header.column.toggleSorting(false, false);
    }
  };

  return (
    <>
      <TableToolbar
        entriesPerPage={entriesPerPage}
        canSearch={canSearch}
        filtersComponent={filtersComponent}
        setEntriesPerPage={setEntriesPerPage}
        pageSize={pageSize}
        setRefreshHandler={setRefreshHandler}
        refreshHandler={refreshHandler}
        setPagination={setPagination}
        getFiltersFromURL={getFiltersFromURL}
      />

      <TableContainer sx={{ boxShadow: 'none' }}>
        <Table>
          <MDBox component='thead'>
            {getHeaderGroups().map((headerGroup, key) => (
              <TableRow key={key}>
                {headerGroup.headers.map((header, idx) => {
                  return (
                    <DataTableHeadCell
                      key={`header-${idx}`}
                      onClick={() => {
                        sortColumn(header);
                      }}
                      width={header.width ? header.width : 'auto'}
                      align={header.align ? header.align : 'left'}
                      sorted={sorting[0]?.id == header.id ? (sorting[0]?.desc == true ? 'desc' : 'asc') : 'none'}
                    >
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </DataTableHeadCell>
                  );
                })}
              </TableRow>
            ))}
          </MDBox>
          <TableBody>
            {rows.map((row, key) => {
              return (
                <TableRow key={`row-${key}`}>
                  {row.getVisibleCells().map((cell, idx) => (
                    <DataTableBodyCell
                      key={`cell-${idx}`}
                      noBorder={noEndBorder && rows.length - 1 === key}
                      align={cell.column.align ? cell.column.align : 'left'}
                    >
                      <pre>
                        {cell.column.columnDef.markCustom &&
                        flexRender(cell.column.columnDef.cell, cell.getContext()).props.renderValue() ? (
                          <MDBadge
                            variant='gradient'
                            color='info'
                            size='xs'
                            badgeContent={flexRender(cell.column.columnDef.cell, cell.getContext())}
                            container
                          />
                        ) : (
                          flexRender(cell.column.columnDef.cell, cell.getContext())
                        )}
                      </pre>
                    </DataTableBodyCell>
                  ))}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        <PaginationButtons
          showTotalEntries={showTotalEntries}
          entriesStart={entriesStart}
          entriesEnd={entriesEnd}
          allEntity={allEntity}
          pageOptions={pageOptions}
          pageIndex={pageIndex}
          tableTheme={tableTheme}
          setPageIndex={setPageIndex}
          getCanPreviousPage={getCanPreviousPage}
          getCanNextPage={getCanNextPage}
          previousPage={previousPage}
          nextPage={nextPage}
        />
      </TableContainer>
    </>
  );
}

// Typechecking props for the DataTable
DataTable.propTypes = {
  entriesPerPage: PropTypes.oneOfType([
    PropTypes.shape({
      defaultValue: PropTypes.number,
      entries: PropTypes.arrayOf(PropTypes.number)
    }),
    PropTypes.bool
  ]),
  canSearch: PropTypes.bool,
  showTotalEntries: PropTypes.bool,
  table: PropTypes.objectOf(PropTypes.array).isRequired,
  tableTheme: PropTypes.shape({
    variant: PropTypes.oneOf(['contained', 'gradient']),
    color: PropTypes.oneOf(['primary', 'secondary', 'info', 'success', 'warning', 'error', 'dark', 'light'])
  }),
  isSorted: PropTypes.bool,
  noEndBorder: PropTypes.bool,
  transformData: PropTypes.func,
  fetchingFunction: PropTypes.func,
  refreshData: PropTypes.bool
};

export default DataTable;
