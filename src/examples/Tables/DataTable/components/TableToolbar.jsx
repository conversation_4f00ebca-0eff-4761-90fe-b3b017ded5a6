import React from 'react';
import Autocomplete from '@mui/material/Autocomplete';

// Material Dashboard 2 PRO React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDInput from 'components/MDInput';

import { IconButton } from '@mui/material';
import { Refresh } from '@mui/icons-material';

function TableToolbar({
  entriesPerPage,
  canSearch,
  filtersComponent,
  setEntriesPerPage,
  pageSize,
  setRefreshHandler,
  refreshHandler,
  setPagination,
  getFiltersFromURL
}) {
  const entries =
    entriesPerPage.entries.length > 0
      ? entriesPerPage.entries.map((el) => el.toString())
      : ['5', '10', '15', '20', '25', '50', '100'];
  return (
    <>
      {entriesPerPage || canSearch ? (
        <MDBox display='flex' justifyContent='space-between' alignItems='center' p={3}>
          {entriesPerPage && (
            <MDBox display='flex' alignItems='center'>
              <Autocomplete
                disableClearable
                value={pageSize.toString()}
                options={entries}
                onChange={(event, newValue) => {
                  setEntriesPerPage(parseInt(newValue, 10));
                }}
                size='small'
                sx={{ width: '5rem' }}
                renderInput={(params) => <MDInput {...params} />}
              />
              <MDTypography variant='caption' color='secondary' style={{ marginLeft: '10px' }}>
                entries per page
              </MDTypography>
            </MDBox>
          )}
          {filtersComponent ? (
            <MDBox display='flex' alignItems='center' justifyContent='space-between' width='80%'>
              {React.cloneElement(filtersComponent, { setPagination, getFiltersFromURL })}
            </MDBox>
          ) : (
            <></>
          )}
          <IconButton
            aria-label='refresh'
            color='secondary'
            size='large'
            onClick={() => {
              setRefreshHandler(!refreshHandler);
            }}
          >
            <Refresh />
          </IconButton>
        </MDBox>
      ) : null}
    </>
  );
}

export default TableToolbar;
