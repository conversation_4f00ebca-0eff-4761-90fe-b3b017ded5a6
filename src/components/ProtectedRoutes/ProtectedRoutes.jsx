import { useAuth } from 'context/auth';
import { useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

const ProtectedRoute = ({ navigateAddress }) => {
  const { auth, verifyToken, logout } = useAuth();
  const location = useLocation();

  useEffect(() => {
    const token = localStorage.getItem('token');

    if (token && !auth.loading) {
      verifyToken();
    } else if (!token) {
      logout();
    }
  }, [location.pathname]);

  if (auth.loading) {
    return <div>Loading...</div>; // Render a loading indicator while verifying the token
  }

  return auth.token ? <Outlet /> : <Navigate to={navigateAddress} />;
};

export default ProtectedRoute;
