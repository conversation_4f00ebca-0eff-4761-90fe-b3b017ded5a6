import MDBox from 'components/MDBox';
import React, { useEffect, useState } from 'react';
import { Autocomplete } from '@mui/material';

import MDButton from 'components/MDButton';
import MDInput from 'components/MDInput';
import MDTypography from 'components/MDTypography';
import DatePickerValue from '../components/DatePickerValue';
import { postChangeApllicantExternalId } from 'requests/proxyServer';
import { toast } from 'react-toastify';

const AmlCustimersFilter = ({
  filters,
  setFilters,
  setSubmitHandler,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  sorting
}) => {
  const [stringFields, setStringFields] = useState(filters.search ? filters.search : '');
  const [customerType, setCustomerType] = useState('Individual');
  const [oldId, setOldId] = useState('');
  const [newId, setNewId] = useState('');
  const [sumsubId, setSumsubId] = useState('');
  const [disableUpdateButton, setDisableUpdateButton] = useState(true);

  useEffect(() => {
    if ((oldId || sumsubId) && newId) {
      setDisableUpdateButton(false);
    } else {
      setDisableUpdateButton(true);
    }
  }, [oldId, newId, sumsubId]);

  function handleSubmit(e) {
    e.preventDefault();

    setFilters({
      search: stringFields,
      customerType: customerType,
      startDate: startDate,
      endDate: endDate
    });
    setSubmitHandler((prev) => !prev);
  }

  useEffect(() => {
    if (customerType !== filters.customerType) {
      setFilters((oldFilters) => ({
        ...oldFilters,
        customerType: customerType
      }));
    }
  }, [customerType]);

  const handleStartDateChange = (newValue) => {
    if (newValue.isAfter(endDate)) {
      setEndDate(newValue);
    }
    setStartDate(newValue);
  };

  const handleEndDateChange = (newValue) => {
    if (newValue.isBefore(startDate)) {
      setStartDate(newValue);
    }
    setEndDate(newValue);
  };

  const handleUpdate = async () => {
    if (sumsubId && oldId) {
      toast.error('Only one of the fields can be filled Sumsub ID or Old External ID!', {
        className: 'toast-width-600'
      });
      return;
    }
    const data = {
      ...(oldId ? { oldExternalCustomerId: oldId } : {}),
      ...(sumsubId ? { sumsubId: sumsubId } : {}),
      newExternalCustomerId: newId,
      customerType: customerType
    };
    try {
      setDisableUpdateButton(true);
      const response = await postChangeApllicantExternalId(data);
      setFilters((oldFilters) => ({
        ...oldFilters,
        updateFilter: oldFilters.updateFilter ? !oldFilters.updateFilter : true
      }));
      setNewId('');
      setOldId('');
      setSumsubId('');
      toast.success('Successfully updated!');
    } catch (error) {
      toast.error(<div dangerouslySetInnerHTML={{ __html: error.message }} />, {
        className: 'toast-width-600',
        autoClose: false
      });
    }
    setDisableUpdateButton(false);
  };

  return (
    <MDBox
      sx={{
        width: '100%',
        flexGrow: 1
      }}
    >
      <form autoComplete='off' onSubmit={handleSubmit} style={{ width: '100%' }}>
        <MDBox
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <MDBox p={3}>
            <MDBox width='100%' marginBottom='15px'>
              <DatePickerValue
                startDate={startDate}
                setStartDate={handleStartDateChange}
                endDate={endDate}
                setEndDate={handleEndDateChange}
              />
            </MDBox>

            <MDBox display='flex' flexDirection={{ xs: 'column', lg: 'row' }} gap='16px'>
              <MDInput
                className='remove-arrows-from-input'
                type='text'
                label='Search'
                value={stringFields}
                sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
                onChange={(e) => setStringFields(e.target.value)}
              />

              <MDBox display='flex' alignItems='center' sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}>
                <Autocomplete
                  disableClearable
                  value={customerType}
                  options={['Individual', 'Company']}
                  onChange={(event, newValue) => {
                    setCustomerType(newValue);
                  }}
                  size='small'
                  sx={{ width: '100%' }}
                  renderInput={(params) => {
                    return <MDInput {...params} />;
                  }}
                />
              </MDBox>
            </MDBox>

            <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px'></MDBox>
          </MDBox>

          <MDBox ml={2} display='flex' alignItems='center'>
            <MDButton type='submit' variant='gradient' color='info'>
              Search
            </MDButton>
          </MDBox>
        </MDBox>

        <MDBox
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'center',
            justifyContent: 'center',
            gap: '16px',
            p: 3
          }}
        >
          <MDInput
            type='text'
            label='Sumsub ID'
            value={sumsubId}
            onChange={(e) => setSumsubId(e.target.value)}
            sx={{ width: { xs: '100%', md: '300px' } }}
          />
          <MDInput
            type='text'
            label='Old External ID'
            value={oldId}
            onChange={(e) => setOldId(e.target.value)}
            sx={{ width: { xs: '100%', md: '300px' } }}
          />
          <MDInput
            type='text'
            label='New External ID'
            value={newId}
            onChange={(e) => setNewId(e.target.value)}
            sx={{ width: { xs: '100%', md: '300px' } }}
          />
          <MDButton variant='gradient' color='warning' onClick={handleUpdate} disabled={disableUpdateButton}>
            Update
          </MDButton>
        </MDBox>
      </form>
    </MDBox>
  );
};

export default AmlCustimersFilter;
