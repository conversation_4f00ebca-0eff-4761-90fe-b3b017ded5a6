import MDBox from 'components/MDBox';
import React, { useState, useEffect } from 'react';
import MDButton from 'components/MDButton';
import MDInput from 'components/MDInput';
import { Autocomplete } from '@mui/material';
import { getRoles } from 'requests/roles';
import { toast } from 'react-toastify';

const UserManagementFilter = ({ filters, setFilters, setSubmitHandler, setPagination }) => {
  const [username, setUsername] = useState(filters.username ? filters.username : '');
  const [name, setName] = useState(filters.name ? filters.name : '');
  const [role, setRole] = useState(filters.role ? filters.role : '');
  const [isBlocked, setIsBlocked] = useState(filters.isBlocked !== undefined ? filters.isBlocked : '');
  const [roles, setRoles] = useState([]);

  // Fetch roles for the dropdown
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await getRoles();
        if (response?.data?.data) {
          setRoles(response.data.data.map((role) => role.name));
        }
      } catch (error) {
        toast.error(`Error fetching roles: ${error?.response?.data?.message || error.message}`);
      }
    };

    fetchRoles();
  }, []);

  const handleRoleChange = (_, newValue) => {
    setRole(newValue || '');
  };

  const handleClearFilters = () => {
    setUsername('');
    setRole('');
    setIsBlocked('');
    setName('');

    // Check if there were any filters applied; reset to first page if yes
    const hadFilters = Object.keys(filters).length > 0;
    if (hadFilters && setPagination) {
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }
    setFilters({});
    setSubmitHandler((prev) => !prev);
  };

  const handleIsBlockedChange = (_, newValue) => {
    // sending string as when boolean false is sent it is interpreted as unknown
    if (newValue === 'Yes') {
      setIsBlocked('true');
    } else if (newValue === 'No') {
      setIsBlocked('false');
    } else {
      setIsBlocked('');
    }
  };

  function handleSubmit(e) {
    e.preventDefault();

    const filterObj = { username: username, role: role, isBlocked: isBlocked, name: name };

    // Check if filters have changed; reset to first page if yes
    const filtersChanged = JSON.stringify(filterObj) !== JSON.stringify(filters);
    if (filtersChanged && setPagination) {
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }

    setFilters(filterObj);
    setSubmitHandler((prev) => !prev);
  }

  return (
    <MDBox
      sx={{
        width: '100%',
        flexGrow: 1
      }}
    >
      <form autoComplete='off' onSubmit={handleSubmit} style={{ width: '100%' }}>
        <MDBox
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <MDBox p={3} width='100%'>
            <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px' mb={2}>
              <MDInput
                className='remove-arrows-from-input'
                type='text'
                label='Email'
                fullWidth
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
              <MDInput
                className='remove-arrows-from-input'
                type='text'
                label='Name'
                fullWidth
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </MDBox>
            <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px'>
              <Autocomplete
                options={roles}
                value={role}
                onChange={handleRoleChange}
                renderInput={(params) => <MDInput {...params} label='Role' />}
                sx={{ minWidth: '200px' }}
                fullWidth
                size='small'
              />
              <Autocomplete
                options={['All', 'Yes', 'No']}
                value={isBlocked === '' ? 'All' : isBlocked === 'true' ? 'Yes' : isBlocked === 'false' ? 'No' : 'All'}
                onChange={handleIsBlockedChange}
                renderInput={(params) => <MDInput {...params} label='Is Blocked' />}
                sx={{ minWidth: '200px' }}
                fullWidth
                size='small'
              />
            </MDBox>
          </MDBox>

          <MDBox display='flex' flexDirection='column' gap={1} mr={2}>
            <MDButton type='submit' variant='gradient' color='info'>
              Search
            </MDButton>
            <MDButton variant='outlined' color='error' onClick={handleClearFilters}>
              Clear
            </MDButton>
          </MDBox>
        </MDBox>
      </form>
    </MDBox>
  );
};

export default UserManagementFilter;
