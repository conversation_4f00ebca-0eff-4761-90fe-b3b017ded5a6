import MDBox from 'components/MDBox';
import React, { useState, useEffect } from 'react';
import MDButton from 'components/MDButton';
import MDInput from 'components/MDInput';
import { Autocomplete, Chip } from '@mui/material';

// Match status options based on the matchButton logic in AcquiringEntity.js
const MATCH_STATUS_OPTIONS = [
  'All',
  'In Progress',
  'Failed Requests',
  'Resolved (Approved)',
  'Resolved (Rejected)',
  'Match',
  'No Match',
  'Error'
];

// SumSub status options
const SUMSUB_STATUS_OPTIONS = ['init', 'pending', 'prechecked', 'queued', 'completed', 'onHold', 'no status'];

const AcquiringEntityFilter = ({ filters, setFilters, setSubmitHandler, setPagination, getFiltersFromURL }) => {
  // Initialize state from URL parameters if getFiltersFromURL is available, otherwise use filters prop
  const initializeFilters = () => {
    if (getFiltersFromURL) {
      const urlFilters = getFiltersFromURL();
      return {
        name: urlFilters.name || '',
        registrationNumber: urlFilters.registrationNumber || '',
        mastercardMatchStatuses: urlFilters.mastercardMatchStatuses || [],
        visaMatchStatuses: urlFilters.visaMatchStatuses || [],
        sumsubAnswers: urlFilters.sumsubAnswers || [],
        sumsubStatuses: urlFilters.sumsubStatuses || []
      };
    }
    return {
      name: filters.name || '',
      registrationNumber: filters.registrationNumber || '',
      mastercardMatchStatuses: filters.mastercardMatchStatuses || [],
      visaMatchStatuses: filters.visaMatchStatuses || [],
      sumsubAnswers: filters.sumsubAnswers || [],
      sumsubStatuses: filters.sumsubStatuses || []
    };
  };

  const initialFilters = initializeFilters();

  const [name, setName] = useState(initialFilters.name);
  const [registrationNumber, setRegistrationNumber] = useState(initialFilters.registrationNumber);
  const [mastercardMatchStatuses, setMastercardMatchStatuses] = useState(initialFilters.mastercardMatchStatuses);
  const [visaMatchStatuses, setVisaMatchStatuses] = useState(initialFilters.visaMatchStatuses);
  const [sumsubAnswers, setSumsubAnswers] = useState(initialFilters.sumsubAnswers);
  const [sumsubStatuses, setSumsubStatuses] = useState(initialFilters.sumsubStatuses);

  // Update state when URL parameters change (when user navigates back/forward)
  useEffect(() => {
    if (getFiltersFromURL) {
      const urlFilters = getFiltersFromURL();
      setName(urlFilters.name || '');
      setRegistrationNumber(urlFilters.registrationNumber || '');
      setMastercardMatchStatuses(urlFilters.mastercardMatchStatuses || []);
      setVisaMatchStatuses(urlFilters.visaMatchStatuses || []);
      setSumsubAnswers(urlFilters.sumsubAnswers || []);
      setSumsubStatuses(urlFilters.sumsubStatuses || []);
    }
  }, [getFiltersFromURL]);

  // Handler for multi-select MasterCard Match statuses
  const handleMastercardMatchStatusesChange = (_, newValues) => {
    setMastercardMatchStatuses(newValues);
  };

  // Handler for multi-select Visa Match statuses
  const handleVisaMatchStatusesChange = (_, newValues) => {
    setVisaMatchStatuses(newValues);
  };

  // Handler for multi-select SumSub answers
  const handleSumsubAnswersChange = (_, newValues) => {
    setSumsubAnswers(newValues);
  };

  // Handler for multi-select SumSub statuses
  const handleSumsubStatusesChange = (_, newValues) => {
    setSumsubStatuses(newValues);
  };

  const handleClearFilters = () => {
    setName('');
    setRegistrationNumber('');
    setMastercardMatchStatuses([]);
    setVisaMatchStatuses([]);
    setSumsubAnswers([]);
    setSumsubStatuses([]);

    // Check if there were any filters applied; reset to first page if yes
    const hadFilters = Object.keys(filters).length > 0;
    if (hadFilters && setPagination) {
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }

    setFilters({});
    setSubmitHandler((prev) => !prev);
  };

  function handleSubmit(e) {
    e.preventDefault();

    const filterObj = {
      name: name,
      registrationNumber: registrationNumber,
      mastercardMatchStatuses: mastercardMatchStatuses.length > 0 ? mastercardMatchStatuses : undefined,
      visaMatchStatuses: visaMatchStatuses.length > 0 ? visaMatchStatuses : undefined,
      sumsubAnswers: sumsubAnswers.length > 0 ? sumsubAnswers : undefined,
      sumsubStatuses: sumsubStatuses.length > 0 ? sumsubStatuses : undefined
    };

    // Check if filters have changed; reset to first page if yes
    const filtersChanged = JSON.stringify(filterObj) !== JSON.stringify(filters);
    if (filtersChanged && setPagination) {
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }

    setFilters(filterObj);
    setSubmitHandler((prev) => !prev);
  }

  return (
    <MDBox
      sx={{
        width: '100%',
        flexGrow: 1
      }}
    >
      <form autoComplete='off' onSubmit={handleSubmit} style={{ width: '100%' }}>
        <MDBox
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <MDBox p={3} width='100%'>
            <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px' mb={2}>
              <MDInput
                className='remove-arrows-from-input'
                type='text'
                label='Name'
                fullWidth
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
              <MDInput
                className='remove-arrows-from-input'
                type='text'
                label='Registration Number'
                fullWidth
                value={registrationNumber}
                onChange={(e) => setRegistrationNumber(e.target.value)}
              />
            </MDBox>
            <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px' mb={2}>
              <Autocomplete
                multiple
                options={MATCH_STATUS_OPTIONS.filter((option) => option !== 'All')}
                value={mastercardMatchStatuses}
                onChange={handleMastercardMatchStatusesChange}
                renderInput={(params) => <MDInput {...params} label='MasterCard Match Status' />}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant='outlined' label={option} size='small' {...getTagProps({ index })} />
                  ))
                }
                sx={{ minWidth: '200px' }}
                fullWidth
                size='small'
              />
              <Autocomplete
                multiple
                options={MATCH_STATUS_OPTIONS.filter((option) => option !== 'All')}
                value={visaMatchStatuses}
                onChange={handleVisaMatchStatusesChange}
                renderInput={(params) => <MDInput {...params} label='Visa Match Status' />}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant='outlined' label={option} size='small' {...getTagProps({ index })} />
                  ))
                }
                sx={{ minWidth: '200px' }}
                fullWidth
                size='small'
              />
            </MDBox>
            <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px'>
              <Autocomplete
                multiple
                options={['RED', 'GREEN', 'No Answer']}
                value={sumsubAnswers}
                onChange={handleSumsubAnswersChange}
                renderInput={(params) => <MDInput {...params} label='SumSub Answer' />}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant='outlined' label={option} size='small' {...getTagProps({ index })} />
                  ))
                }
                sx={{ minWidth: '200px' }}
                fullWidth
                size='small'
              />
              <Autocomplete
                multiple
                options={SUMSUB_STATUS_OPTIONS}
                value={sumsubStatuses}
                onChange={handleSumsubStatusesChange}
                renderInput={(params) => <MDInput {...params} label='SumSub Status' />}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant='outlined' label={option} size='small' {...getTagProps({ index })} />
                  ))
                }
                sx={{ minWidth: '200px' }}
                fullWidth
                size='small'
              />
            </MDBox>
          </MDBox>

          <MDBox display='flex' flexDirection='column' gap={1} mr={2}>
            <MDButton type='submit' variant='gradient' color='info'>
              Search
            </MDButton>
            <MDButton variant='outlined' color='error' onClick={handleClearFilters}>
              Clear
            </MDButton>
          </MDBox>
        </MDBox>
      </form>
    </MDBox>
  );
};

export default AcquiringEntityFilter;
