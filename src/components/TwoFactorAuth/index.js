import { useNavigate, useLocation } from 'react-router-dom';
import { validate2FA } from 'requests/user';
import { useAuth } from 'context/auth';
import { useState } from 'react';
import PropTypes from 'prop-types';
import MDBox from 'components/MDBox';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';
import { Alert, CircularProgress } from '@mui/material';

function TwoFactorAuth({
  mode = 'verify',
  qrCodeUrl,
  setupError,
  buttonText,
  inputLabel = '6-digit code',
  encrypted2FASecret
}) {
  const { setAuth } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { twoFAToken } = location.state || {};
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onVerify = async (code) => {
    try {
      if (!twoFAToken) {
        throw new Error('Token is missing. Please try logging in again.');
      }
      const response = await validate2FA(twoFAToken, code, encrypted2FASecret);

      // Store token and username in localStorage
      localStorage.token = response.data.token;
      localStorage.userName = response.data.username;

      // Update auth context
      setAuth({ token: true, login: false });

      // Navigate to dashboard
      navigate('/dashboards/aml/transactions', { replace: true });
    } catch (error) {
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!code || code.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setIsSubmitting(true);
    try {
      await onVerify(code);
    } catch (err) {
      setError(err.message || 'Verification failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MDBox>
      {/* Setup error message */}
      {setupError && (
        <MDBox mb={3}>
          <Alert severity='error' variant='outlined' sx={{ color: '#fa6161', fontSize: 14 }}>
            {setupError}
          </Alert>
        </MDBox>
      )}

      {/* QR Code section (only for setup mode) */}
      {mode === 'setup' && (
        <MDBox mb={3} textAlign='center'>
          {qrCodeUrl ? (
            <MDBox
              component='img'
              src={qrCodeUrl}
              alt='QR Code for Google Authenticator'
              width='200px'
              height='200px'
              mx='auto'
              my={2}
              borderRadius='lg'
              boxShadow='md'
            />
          ) : !setupError ? (
            <MDBox display='flex' justifyContent='center' my={3}>
              <CircularProgress color='info' />
            </MDBox>
          ) : null}
        </MDBox>
      )}

      {/* Verification form */}
      <MDBox component='form' role='form' onSubmit={handleSubmit}>
        <MDBox mb={2}>
          <MDInput
            type='text'
            label={inputLabel}
            value={code}
            onChange={(e) => {
              // Only allow numbers and limit to 6 digits
              const value = e.target.value.replace(/[^0-9]/g, '').slice(0, 6);
              setCode(value);
            }}
            fullWidth
            inputProps={{
              inputMode: 'numeric',
              pattern: '[0-9]*',
              maxLength: 6
            }}
          />
        </MDBox>
        {error && (
          <MDBox mb={2}>
            <Alert severity='error' variant='outlined' sx={{ color: '#fa6161', fontSize: 14 }}>
              {error}
            </Alert>
          </MDBox>
        )}
        <MDBox mt={3} mb={1}>
          <MDButton
            variant='gradient'
            color='info'
            size='large'
            fullWidth
            type='submit'
            disabled={isSubmitting || code.length !== 6}
          >
            {buttonText}
          </MDButton>
        </MDBox>
      </MDBox>
    </MDBox>
  );
}

// Typechecking props
TwoFactorAuth.propTypes = {
  mode: PropTypes.oneOf(['verify', 'setup']),
  qrCodeUrl: PropTypes.string,
  setupError: PropTypes.string,
  buttonText: PropTypes.string
};

export default TwoFactorAuth;
