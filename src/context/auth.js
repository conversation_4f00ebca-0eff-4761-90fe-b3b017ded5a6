import React, { createContext, useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';
import { toast } from 'react-toastify';

import { getCheckUserToken } from 'requests/user';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [auth, setAuth] = useState({ token: null, loading: true });
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      verifyToken();
    } else {
      setAuth({ token: false, loading: false });
    }
  }, []);

  const verifyToken = async () => {
    try {
      const response = await getCheckUserToken();

      if (response.data.valid) {
        setAuth({ token: true, loading: false });
        decodeToken();
      } else {
        setAuth({ token: false, loading: false });
        logout();
      }
    } catch (error) {
      setAuth({ token: false, loading: false });
      logout();
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userName');
    setAuth({ token: false, loading: false });
    navigate('authentication/sign-in');
  };

  const decodeToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const decoded = jwtDecode(token);
        isTokenExpiringAfterFiveMin(decoded);
      } catch (error) {
        console.error('Invalid token', error);
      }
    }
  };

  const isTokenExpiringAfterFiveMin = (decoded) => {
    const toastId = 'toast-expire';
    let expiringTimeLessDent5min = false;
    let timeExpirition = 0;
    if (decoded && decoded.exp) {
      const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
      const expTime = decoded.exp;
      const threshold = 5 * 60; // 5 minutes in seconds
      timeExpirition = expTime - currentTime;
      expiringTimeLessDent5min = timeExpirition <= threshold;
    } else {
      expiringTimeLessDent5min = false;
    }
    if (expiringTimeLessDent5min) {
      toast.warning(`Your session will expire after ${(timeExpirition / 60).toFixed(2)} minutes.`, {
        toastId,
        className: 'toast-width-600'
      });
    }
  };

  return <AuthContext.Provider value={{ auth, setAuth, verifyToken, logout }}>{children}</AuthContext.Provider>;
};

export const useAuth = () => useContext(AuthContext);
