import axios from 'axios';
import { toast } from 'react-toastify';
import { updateParams } from 'helpers/params';

export const getCheckUserToken = async () => {
  return await axios.get(`${process.env.REACT_APP_IL_API}/verify-token`, {
    headers: {
      Authorization: `Bearer ${localStorage.token}`
    }
  });
};

export const createUser = async (body) => {
  try {
    if (!body.username.trim()) {
      throw new Error('Username cannot be empty');
    }
    if (!body.name.trim()) {
      throw new Error('Name cannot be empty');
    }

    return await axios.post(
      `${process.env.REACT_APP_IL_API}/users`,
      {
        username: body.username,
        role: body.role,
        name: body.name
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
  } catch (error) {
    throw await errorHandler(error);
  }
};

export const getUsers = async (limit, page, filters, sorting) => {
  const queryParams = {};

  const params = updateParams(queryParams, limit, page, filters, sorting);

  try {
    const response = await axios.get(`${process.env.REACT_APP_IL_API}/users`, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
    return response;
  } catch (error) {
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const getUserById = async (userId) => {
  try {
    const response = await axios.get(`${process.env.REACT_APP_IL_API}/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      },
      timeout: 12000
    });
    return response.data;
  } catch (error) {
    throw await errorHandler(error);
  }
};

export const updateUser = async (body, userId) => {
  try {
    return await axios.put(
      `${process.env.REACT_APP_IL_API}/users/${userId}`,
      {
        username: body.username,
        role: body.role,
        name: body.name,
        isBlocked: body.isBlocked
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
  } catch (error) {
    throw await errorHandler(error);
  }
};

// updates user's own password
export const updatePassword = async (body) => {
  try {
    if (!body.password.trim()) {
      throw new Error('Password cannot be empty');
    }

    return await axios.post(
      `${process.env.REACT_APP_IL_API}/users/update-password`,
      {
        password: body.password,
        oldPassword: body.oldPassword
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
  } catch (error) {
    throw await errorHandler(error);
  }
};

// resets another user's password
export const resetPassword = async (userId) => {
  try {
    return await axios.post(
      `${process.env.REACT_APP_IL_API}/users/${userId}/reset-password`,
      {},
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
  } catch (error) {
    throw await errorHandler(error);
  }
};

// 2FA validate - validates the code during login
export const validate2FA = async (twoFAToken, code, encrypted2FASecret) => {
  try {
    return await axios.post(`${process.env.REACT_APP_IL_API}/2fa/validate`, { twoFAToken, code, encrypted2FASecret });
  } catch (error) {
    throw await errorHandler(error);
  }
};

// 2FA setup - gets the QR code for Google Authenticator
export const setup2FA = async (twoFAToken) => {
  try {
    return await axios.post(`${process.env.REACT_APP_IL_API}/2fa/setup`, { twoFATempToken: twoFAToken });
  } catch (error) {
    throw await errorHandler(error);
  }
};

async function errorHandler(error) {
  if (error.message && !error.response) {
    return error;
  }

  if (error.response?.data?.messages) {
    const formattedMessage = error.response.data.messages[0].split('\n').join('<br>');
    return new Error(formattedMessage);
  }

  if (error.response?.data?.message) {
    const formattedMessage = error.response.data.message.split('\n').join('<br>');
    return new Error(formattedMessage);
  }

  return new Error('Something went wrong!');
}
