import axios from 'axios';
import { updateParams } from 'helpers/params';
import { toast } from 'react-toastify';

const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_IL_API,
  headers: {
    'Content-Type': 'application/json'
  }
});

export const getVisaTransactions = async (limit, page, filters, sorting) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);

    return await axios.get(`${process.env.REACT_APP_IL_API}/transactions/visa`, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
  } catch (error) {
    toast.error(error.response?.data?.message || error.message);
  }
};

export const getSumsubTransactions = async (limit, page, filters, sorting, downloadAll) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting, downloadAll);
    return await axios.get(`${process.env.REACT_APP_IL_API}/transactions/ln`, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
  } catch (error) {
    toast.error(error.response?.data?.message || error.message);
  }
};

export const getVisaAccountBalances = async (limit, page, filters, sorting) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);

    return await axios.get(`${process.env.REACT_APP_IL_API}/account-balances`, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
  } catch (error) {
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const signIn = async (username, password) => {
  try {
    return await axios.post(`${process.env.REACT_APP_IL_API}/sign-in`, {
      username,
      password
    });
  } catch (error) {
    throw error;
  }
};

export const getVisaInvalidTransactions = async (limit, page, filters, sorting) => {
  try {
    let url = `${process.env.REACT_APP_IL_API}/transactions/visa/invalid-payouts`;

    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);

    return await axios.get(url, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
  } catch (error) {
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const getVisaInvalidTransaction = async (referenceId) => {
  try {
    return await axios.get(`${process.env.REACT_APP_IL_API}/transactions/visa/invalid-payouts/${referenceId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
  } catch (error) {
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const updateVisaInvalidTransaction = async (body) => {
  try {
    return await axios.post(
      `${process.env.REACT_APP_IL_API}/resend-visa-transfer`,
      {
        payload: body
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
  } catch (error) {
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const rejectVisaInvalidTransaction = async (referenceId) => {
  try {
    return await axios.post(
      `${process.env.REACT_APP_IL_API}/reject-visa-transfer`,
      {
        referenceId: referenceId
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
  } catch (error) {
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const getTransactionPDF = async (uniqueId) => {
  try {
    const response = await axiosInstance.get(`${process.env.REACT_APP_IL_API}/proof-of-payment/pdf/${uniqueId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.token}`,
        Accept: 'application/pdf' // Allow server to respond with PDF data.
      },
      responseType: 'blob'
    });
    // Create a Blob URL for download
    const blob = new Blob([response.data], { type: 'application/pdf' });

    // Create a temporary link to download the PDF
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);

    // Extract the filename from Content-Disposition header, fallback to a default name
    const contentDisposition = response.headers['content-disposition'];
    const fileName = contentDisposition ?? `proof_of_payment.pdf`;

    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(link.href); // Free memory
  } catch (error) {
    console.error('Error while downloading PDF:', error);
    toast.error(error.response?.data?.message || error.message, {
      className: 'toast-error'
    });
  }
};

export const getTransactionFromOracleByTransactionId = async (limit, page, filters, sorting) => {
  try {
    const queryParams = {};
    let endPoint = ``;

    if (filters.search?.length > 0 && filters.uniqueId?.length > 0) {
      throw new Error('You need to provide only one of the fileds!');
    }

    if (filters.uniqueId) {
      const convertToNumber = Number(filters.uniqueId);
      if (isNaN(convertToNumber)) {
        throw new Error('Invalid unique id need to be a number');
      } else {
        endPoint = `/transaction/completed/unique-id/${convertToNumber}`;
      }
    }

    if (filters.search) {
      endPoint = `/transaction/completed/by-id-or-number/${filters.search}`;
    }

    if (endPoint) {
      const result = await axios.get(`${process.env.REACT_APP_IL_API}${endPoint}`, {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      });
      if (result.data?.data) {
        return { data: { docs: [result.data?.data] } };
      } else {
        throw new Error('Transaction data not found');
      }
    } else {
      return { data: { docs: [] } };
    }
  } catch (error) {
    toast.error(error.message, { className: 'toast-width-600' });
    return { data: [] };
  }
};
