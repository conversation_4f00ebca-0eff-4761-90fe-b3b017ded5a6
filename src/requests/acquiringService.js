import axios from 'axios';
import { updateParams } from 'helpers/params';
import { toast } from 'react-toastify';

export const getMastercardMatchProMatches = async (
  limit,
  page,
  filters,
  sorting,
  entityId,
  mastercardRefNumberMatched
) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);
    const response = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mastercard-match-pro-dashboard/${entityId}?mastercardRefNumberMatched=${mastercardRefNumberMatched}`,
      {
        params,
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        },
        timeout: 12000
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getVisaVmssMatches = async (limit, page, filters, sorting, entityId) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);
    const response = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/visa-vmss-match-dashboard/${entityId}`,
      {
        params,
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        },
        timeout: 12000
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAcquiringEntities = async (limit, page, filters, sorting) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);

    const result = await axios.get(`${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/acquiring-entities`, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
    return result.data;
  } catch (error) {
    try {
      handleError(error);
    } catch (error) {
      toast.error(error.message);
    }
  }
};

export const changeMastercardMatchStatusManually = async (acquiringEntityId, typeOfAction, matchDataId) => {
  try {
    const body = {
      acquiringEntityId,
      typeOfAction,
      matchDataId
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mastercard-match-pro-handle-manual-acquire`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const changeMastercardMatchStatusManuallyByRefNumber = async (
  acquiringEntityId,
  typeOfAction,
  mastercardRefNumberMatched
) => {
  try {
    const body = {
      acquiringEntityId,
      typeOfAction,
      mastercardRefNumberMatched
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mastercard-match-pro-handle-manual-acquire-by-ref-number`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const changeVisaVmssMatchStatusManually = async (acquiringEntityId, typeOfAction, matchDataId) => {
  try {
    const body = {
      acquiringEntityId,
      typeOfAction,
      matchDataId
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/visa-vmss-handle-manual-acquire`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const makeNewMatchInMastercardMatchPro = async (acquiringEntityId) => {
  try {
    const body = {
      acquiringEntityId
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mastercard-match-pro`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAcquiringEntityByIdWithFullDetails = async (acquiringEntityId) => {
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/acquiring-entity-with-all-properties/${acquiringEntityId}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        },
        timeout: 12000
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getPrincipalVales = async () => {
  try {
    const response = await axios.get(`${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/principal/get-values`, {
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      },
      timeout: 12000
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export async function updateAcquiringEntity(body) {
  try {
    const response = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/acquiring-entity/update`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );

    return response.data;
  } catch (error) {
    handleError(error);
  }
}

export const getMastercardMatchProReferences = async (limit, page, filters, sorting, entityId) => {
  try {
    const queryParams = {};
    const params = updateParams(queryParams, limit, page, filters, sorting);
    const response = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mastercard-match-pro-all-match-reference-dashboard/${entityId}`,
      {
        params,
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        },
        timeout: 12000
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

function handleError(error) {
  let message = 'Something went wrong!';
  if (error.response?.data?.message) {
    message = error.response?.data?.message;
  } else if (error.response?.data?.messages) {
    // return array of messages
    message = error.response?.data?.messages.join('\n ');
  } else if (error.message) {
    message = error.message;
  }
  throw new Error(message);
}

export const makeNewVisaVmssMatch = async (acquiringEntityId) => {
  try {
    const body = {
      acquiringEntityId
    };

    const result = await axios.post(`${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/visa-vmss-match`, body, {
      headers: {
        Authorization: `Bearer ${localStorage.token}`
      }
    });
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const sendMidConfiguration = async (acquiringEntity, parentMerchantId, pspNumber) => {
  try {
    const body = {
      acquiringEntity,
      parentMerchantId,
      pspNumber
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mid-configuration/send`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const resetMidConfiguration = async (acquiringEntity) => {
  try {
    const body = {
      acquiringEntity
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/mid-configuration/reset`,
      body,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const createAcquiringEntity = async (entityData) => {
  try {
    const response = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/acquiring-entity/create`,
      entityData,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retryOnboarding = async (path, acquiringEntityId) => {
  try {
    const body = {
      acquiringEntityId
    };

    const result = await axios.post(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/${path}/retry/${acquiringEntityId}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data;
  } catch (error) {
    handleError(error);
  }
};

export const downloadMastercardMatchProPdf = async (acquiringEntityId) => {
  try {
    const result = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/generate-pdf-mastercard-match-report/${acquiringEntityId}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );
    return result.data.data;
  } catch (error) {
    handleError(error);
  }
};

export const downloadVisaVmssPdf = async (acquiringEntityId) => {
  try {
    const result = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/generate-pdf-vmss-match-report/${acquiringEntityId}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        }
      }
    );

    return result.data.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAcquiringEntityFiles = async (entityId) => {
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/acquiring-entity/files/${entityId}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.token}`
        },
        timeout: 12000
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const downloadAcquiringEntityFile = async (entityId, fileId, fileName) => {
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_RYVYL_ACQUIRING_API}/dashboard/acquiring-entity/files/${entityId}/download/${fileId}`,
      {
        params: fileName ? { fileName } : {},
        headers: {
          Authorization: `Bearer ${localStorage.token}`,
          Accept: 'application/octet-stream'
        },
        responseType: 'blob',
        timeout: 30000
      }
    );
    // Get the MIME type from the response headers
    const contentType = response.headers['content-type'] || 'application/octet-stream';

    // Create a Blob URL for download with the correct MIME type
    const blob = new Blob([response.data], { type: contentType });

    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);

    // Extract the filename from Content-Disposition header, fallback to provided name
    const contentDisposition = response.headers['content-disposition'];
    const extractedFileName = contentDisposition
      ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
      : fileName || `entity_file_${fileId}`;

    link.download = extractedFileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(link.href); // Free memory
  } catch (error) {
    handleError(error);
  }
};
