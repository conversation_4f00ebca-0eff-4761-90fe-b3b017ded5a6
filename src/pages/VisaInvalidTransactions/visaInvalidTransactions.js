import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

import DataTable from 'examples/Tables/DataTable/DataTable';
import tableData from './TransactionDataTable';
import { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { getVisaInvalidTransactions, rejectVisaInvalidTransaction } from 'requests/transaction';
import Footer from 'examples/Footer';
import { Button, Icon, IconButton, Tooltip } from '@mui/material';
import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';
import { useAuth } from 'context/auth';
import DialogPopup from 'components/DialogPopup/DialogPopup';
import MDButton from 'components/MDButton';
import { toast } from 'react-toastify';

function VisaInvalidTransactions() {
  const { logout } = useAuth();

  const [tableInfo, setTableInfo] = useState({ ...tableData });
  const [transactionReferenceId, setTransactionReferenceId] = useState(undefined);
  const [dialogSingInOpen, setDialogSingInOpen] = useState(false);
  const [dialogRejectTransactionOpenBtnDisabled, setDialogRejectTransactionOpenBtnDisabled] = useState(false);
  const [dialogRejectTransactionOpen, setDialogRejectTransactionOpen] = useState(false);
  const [refreshData, setRefreshData] = useState(false);
  const [sorting, setSorting] = useState([]);

  const transformData = (data) => {
    if (data.length > 0) {
      const rows = data.map((el) => {
        const resolved = el.resolved ? 'True' : 'False';
        return {
          ...el,
          resolved: resolved,
          action: (
            <MDBox display='flex'>
              {editButton(el.referenceId)}
              {rejectButton(el.referenceId, setDialogRejectTransactionOpen, setTransactionReferenceId)}
            </MDBox>
          )
        };
      });
      return rows;
    } else {
      return [];
    }
  };

  const redirectToSignIn = () => {
    setDialogSingInOpen(false);
    logout();
  };

  const handleRejectTransaction = async () => {
    setDialogRejectTransactionOpenBtnDisabled(true);
    try {
      await rejectVisaInvalidTransaction(transactionReferenceId);
      toast.success(`You reject transaction successfully.`, {
        className: 'toast-width-600'
      });
      setRefreshData(!refreshData);
    } catch (error) {
      toast.error(`Error: ${error?.response?.data?.message}`, {
        className: 'toast-width-600'
      });
    }
    setDialogRejectTransactionOpen(false);
    setDialogRejectTransactionOpenBtnDisabled(false);
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <DataTable
        table={tableInfo}
        canSearch
        transformData={transformData}
        fetchingFunction={getVisaInvalidTransactions}
        refreshData={refreshData}
        sorting={sorting}
        setSorting={setSorting}
      />

      <DialogPopup
        open={dialogSingInOpen}
        title={'Session Expired!'}
        description={'Sign In again to continue the session.'}
        buttonsActions={<Button onClick={redirectToSignIn}>SIGN IN</Button>}
      />

      <DialogPopup
        open={dialogRejectTransactionOpen}
        title={'Reject transaction!'}
        description={'Are you sure you want to reject transaction?.'}
        buttonsActions={
          <>
            <MDButton
              variant='text'
              color='error'
              disabled={dialogRejectTransactionOpenBtnDisabled}
              onClick={() => setDialogRejectTransactionOpen(false)}
            >
              No
            </MDButton>
            <MDButton
              variant='text'
              color='success'
              disabled={dialogRejectTransactionOpenBtnDisabled}
              onClick={handleRejectTransaction}
            >
              yes
            </MDButton>
          </>
        }
      />

      <Footer />
    </DashboardLayout>
  );
}

function editButton(referenceId) {
  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
      <Tooltip title='Edit'>
        <NavLink to={`/dashboards/visa-direct/stuck-transactions/edit/${referenceId}`}>
          <MDTypography fontSize='0.875rem'>
            <IconButton size='small' color='info'>
              <Icon fontSize='small'>edit</Icon>
            </IconButton>
          </MDTypography>
        </NavLink>
      </Tooltip>
    </MDBox>
  );
}

function rejectButton(referenceId, setDialogRejectTransactionOpen, setTransactionReferenceId) {
  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
      <Tooltip title='Reject transaction'>
        <MDTypography fontSize='0.875rem'>
          <IconButton
            size='small'
            color='info'
            onClick={() => {
              setDialogRejectTransactionOpen(true);
              setTransactionReferenceId(referenceId);
            }}
          >
            <Icon fontSize='small'>close</Icon>
          </IconButton>
        </MDTypography>
      </Tooltip>
    </MDBox>
  );
}

export default VisaInvalidTransactions;
