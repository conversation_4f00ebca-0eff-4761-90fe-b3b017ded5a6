import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

const RenderJsonValue = ({ data, info, currentKey, value, principalNumber, path, compareMatch }) => {
  if (value === null || value === undefined) {
    return (
      <MDTypography color='white' variant='body2'>
        -
      </MDTypography>
    );
  }

  if (typeof value === 'object') {
    return (
      <JsonRenderer
        data={value}
        info={info}
        principalNumber={principalNumber}
        path={path + currentKey}
        compareMatch={compareMatch}
      />
    );
  }

  const merchantMatch = info.row.original?.merchantMatch;
  let color = 'white';

  if (principalNumber !== null) {
    if (
      currentKey === 'firstName' ||
      currentKey === 'lastName' ||
      currentKey === 'middleInitial' ||
      currentKey === 'name'
    ) {
      color = compareMatch(merchantMatch?.principalsMatch?.[principalNumber - 1], 'name');
    } else if (path?.includes('address')) {
      color = compareMatch(merchantMatch?.principalsMatch?.[principalNumber - 1], 'address');
    } else if (path?.includes('driversLicense')) {
      color = compareMatch(merchantMatch?.principalsMatch?.[principalNumber - 1], 'driversLicense');
    } else {
      color = compareMatch(merchantMatch?.principalsMatch?.[principalNumber - 1], currentKey);
    }
  } else {
    const a = merchantMatch?.hasOwnProperty(info.column.id);
    color = compareMatch(merchantMatch, info.column.id);
  }

  return (
    <MDTypography sx={{ color: `${color}!important` }} variant='body2' component='span'>
      {String(value)}
    </MDTypography>
  );
};

export const JsonRenderer = ({ info, data, principalNumber, path = '', compareMatch }) => {
  if (!data) return null;
  return (
    <MDBox
      sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))'
      }}
    >
      {Object.entries(data).map(([key, value]) => (
        <MDBox
          key={key}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <MDTypography variant='caption' color='text' fontWeight='medium' textTransform='capitalize' component='span'>
            {key}:-
          </MDTypography>
          <RenderJsonValue
            data={data}
            info={info}
            currentKey={key}
            value={value}
            principalNumber={principalNumber}
            path={path}
            compareMatch={compareMatch}
          />
        </MDBox>
      ))}
    </MDBox>
  );
};
