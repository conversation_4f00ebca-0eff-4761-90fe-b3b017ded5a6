import MDBox from 'components/MDBox';
import React, { useState } from 'react';

import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import { makeNewVisaVmssMatch } from 'requests/acquiringService';
import { toast } from 'react-toastify';
import DialogPopup from 'components/DialogPopup/DialogPopup';
import { formatDate } from 'helpers/dateFormater';
import DownloadPdfButton from 'pages/AcquiringEntity/components/DownloadPdfButton';

const VisaVmssTableHeader = ({
  acquiringEntityId,
  lastMatch,
  failedMatchRequests,
  totalRequests,
  setSubmitHandler
}) => {
  const [openDialog, setOpenDialog] = useState(false);

  async function callNewMastercardMatch() {
    if (!acquiringEntityId) {
      toast.error('No acquiring entity selected!');
      return;
    }
    const loadingToast = toast.loading('Fetching information...');
    try {
      const result = await makeNewVisaVmssMatch(acquiringEntityId);
      if (result?.data?.terminationInquiryResponse) {
        toast.success('New match created successfully!');
      }
      setSubmitHandler((prev) => !prev);
      toast.dismiss(loadingToast);
    } catch (error) {
      toast.dismiss(loadingToast);
      let message = '';

      if (error.message.includes('\n')) {
        message = error.message.split('\n');
      } else {
        message = error.message;
      }

      // If error.message is an array, join it with line breaks
      if (Array.isArray(message)) {
        message = (
          <>
            {message.map((item, index) => (
              <p key={index}>{item}</p>
            ))}
          </>
        );
      }

      toast.error(message, {
        className: 'toast-width-600',
        autoClose: false,
        closeOnClick: false
      });
    }
  }

  function resetState() {
    setOpenDialog(false);
  }

  return (
    <MDBox
      sx={{
        width: '100%',
        flexGrow: 1
      }}
    >
      <MDBox
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <MDBox p={3}>
          <MDBox>
            <MDBox display='flex' flexDirection={{ xs: 'column', lg: 'row' }} gap='16px'>
              <MDTypography
                variant='body2'
                color='text'
                fontWeight='medium'
                textTransform='capitalize'
                component='span'
              >
                Last check date:
              </MDTypography>
              <MDTypography
                variant='body2'
                color='text'
                fontWeight='medium'
                textTransform='capitalize'
                component='span'
              >
                {lastMatch ? formatDate(lastMatch) : 'Date not available'}
              </MDTypography>
              <MDBox display='flex' alignItems='center'></MDBox>
            </MDBox>
            <MDBox display='flex' flexDirection={{ xs: 'column', lg: 'row' }} gap='16px'>
              <MDTypography
                variant='body2'
                color='text'
                fontWeight='medium'
                textTransform='capitalize'
                component='span'
              >
                Failed match requests:
              </MDTypography>
              <MDTypography
                variant='body2'
                color='text'
                fontWeight='medium'
                textTransform='capitalize'
                component='span'
              >
                {failedMatchRequests} of {totalRequests}
              </MDTypography>
              <MDBox display='flex' alignItems='center'></MDBox>
            </MDBox>
          </MDBox>

          <MDBox display='flex' flexDirection={{ xs: 'column', md: 'row' }} gap='16px'></MDBox>
        </MDBox>

        <MDBox ml={2} display='flex' alignItems='center' gap={2}>
          <MDButton type='submit' variant='gradient' color='info' onClick={() => setOpenDialog(true)}>
            New Match
          </MDButton>
          <DownloadPdfButton acquiringEntityId={acquiringEntityId} type='visa' />
        </MDBox>
      </MDBox>
      <DialogPopup
        open={openDialog}
        title={'Make new Match request'}
        description='Are you sure you want to make a new Match request?'
        buttonsActions={
          <>
            <MDButton
              variant='text'
              color='error'
              onClick={() => {
                resetState();
              }}
            >
              No
            </MDButton>
            <MDButton
              variant='text'
              color='success'
              onClick={async () => {
                await callNewMastercardMatch();
                resetState();
              }}
            >
              Yes
            </MDButton>
          </>
        }
      />
    </MDBox>
  );
};

export default VisaVmssTableHeader;
