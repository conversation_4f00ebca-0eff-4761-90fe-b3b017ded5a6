import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

import tableData from './VisaVmssDataTable';
import { useCallback, useEffect, useMemo, useState } from 'react';
import Footer from 'examples/Footer';

import { getVisaVmssMatches } from 'requests/acquiringService';
import MDBox from 'components/MDBox';
import { IconButton, Badge } from '@mui/material';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import ExpandingDataTable from 'examples/Tables/DataTable/ExpandingDataTable';
import { JsonRenderer } from './components/ColumnExpansion';
import VisaVmssTableHeader from './components/TableHeader';
import { changeVisaVmssMatchStatusManually } from 'requests/acquiringService';
import ActionButtons from 'components/matches/ActionButtons';

function VisaVmss() {
  const [tableInfo, setTableInfo] = useState(tableData);
  const [filters, setFilters] = useState({});
  const [submitHandler, setSubmitHandler] = useState(false);
  const [sorting, setSorting] = useState([]);
  const { acquiringEntityId } = useParams();
  const [lastMatch, setLastMatch] = useState(null);
  const [matchDataId, setMatchDataId] = useState(null);
  const [failedMatchRequests, setFailedMatchRequests] = useState(null);
  const [totalRequests, setTotalRequests] = useState(null);

  // Add cleanup effect
  useEffect(() => {
    return () => {
      // This will run when component unmounts
      toast.dismiss();
    };
  }, []);

  let dynamicColumnsData;
  const dataTableData = useCallback(async (limit, page, filters, sorting) => {
    const loadingToast = toast.loading('Fetching information...');

    try {
      const result = await getVisaVmssMatches(limit, page, filters, sorting, acquiringEntityId);

      setFailedMatchRequests(result.data.failedRequests);
      setLastMatch(result.data.createdAt);
      setTotalRequests(result.data.totalRequests);
      setMatchDataId(result.data._id);
      dynamicColumnsData = result?.data?.columnsData;

      toast.dismiss(loadingToast);

      return result;
    } catch (error) {
      toast.dismiss(loadingToast);
      let message = '';

      if (error.message.includes('\n')) {
        message = error.message.split('\n');
      } else {
        message = error.message;
      }

      // If error.message is an array, show each message in a new line
      if (Array.isArray(message)) {
        message = (
          <>
            {message.map((item, index) => (
              <p key={index}>{item}</p>
            ))}
          </>
        );
      }

      toast.error(message, {
        className: 'toast-width-600',
        autoClose: false,
        closeOnClick: false
      });
    }
  }, []);

  const dynamicColumns = useCallback(() => {
    const doNotShowColumns = ['_id', 'merchantMatch'];
    let columns;
    if (dynamicColumnsData && dynamicColumnsData.length > 0) {
      columns = dynamicColumnsData.map((el, i) => {
        if (doNotShowColumns.includes(el)) {
          return null;
        }

        return {
          header: el,
          accessorKey: el,
          cell: (info) => {
            let principalNumber = null;
            const match = el.match(/^principals(\d+)$/);
            if (match) {
              principalNumber = match[1];
            }
            const columnValue = info.getValue();
            if (typeof columnValue === 'object' && columnValue !== null) {
              let hasMatch = false;
              if (info.column.id?.includes('principals')) {
                hasMatch = containsValues(info.row.original?.merchantMatch?.principalsMatch);
              } else {
                const color = compareMatch(info.row.original?.merchantMatch, info.column.id);
                hasMatch = color !== 'white';
              }

              return (
                <>
                  {hasMatch ? (
                    // {principalNumber && hasMatch ? (
                    <Badge
                      color='error'
                      variant='dot'
                      sx={{
                        '& .MuiBadge-badge': {
                          right: 4,
                          top: 4
                        }
                      }}
                    >
                      {info.row.getCanExpand() ? (
                        <IconButton
                          aria-label='expand row'
                          size='small'
                          color='info'
                          onClick={info.row.getToggleExpandedHandler()}
                          style={{ cursor: 'pointer' }}
                        >
                          {info.row.getIsExpanded() ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}{' '}
                          {`${info.row.original?.[el]?.firstName ?? ''} ${info.row.original?.[el]?.lastName ?? ''}`}
                        </IconButton>
                      ) : (
                        ''
                      )}
                    </Badge>
                  ) : info.row.getCanExpand() ? (
                    <IconButton
                      aria-label='expand row'
                      size='small'
                      color='info'
                      onClick={info.row.getToggleExpandedHandler()}
                      style={{ cursor: 'pointer' }}
                    >
                      {info.row.getIsExpanded() ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}{' '}
                      {`${info.row.original?.[el]?.firstName ?? ''} ${info.row.original?.[el]?.lastName ?? ''}`}
                    </IconButton>
                  ) : (
                    ''
                  )}

                  {info.row.getIsExpanded() && (
                    <JsonRenderer
                      data={info.getValue()}
                      info={info}
                      principalNumber={principalNumber}
                      compareMatch={compareMatch}
                    />
                  )}
                </>
              );
            } else {
              let color = 'white';
              if (el.includes('address')) {
                // color = compareMatch(info.row.original?.merchantMatch, 'addressMatch');
                const separateKey = el.split('-')[1];
                color = compareMatch(info.row.original?.merchantMatch?.addressMatch, separateKey);
              } else {
                color = compareMatch(info.row.original?.merchantMatch, el);
              }

              return (
                <MDBox color={color} sx={{ display: 'flex', justifyContent: 'space-around' }}>
                  {' '}
                  {info.renderValue()}{' '}
                </MDBox>
              );
            }
          },
          footer: (props) => props.column.id
        };
      });

      columns = columns.filter((column) => column !== null);
      return columns;
    }

    return columns || tableData.columns;
  }, []);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <ExpandingDataTable
        entriesPerPage={{ defaultValue: 1000, entries: [1000] }}
        table={tableInfo}
        canSearch
        fetchingFunction={dataTableData}
        submitHandler={submitHandler}
        filters={filters}
        sorting={sorting}
        setSorting={setSorting}
        dynamicColumns={dynamicColumns}
        filtersComponent={
          <VisaVmssTableHeader
            acquiringEntityId={acquiringEntityId}
            lastMatch={lastMatch}
            failedMatchRequests={failedMatchRequests}
            totalRequests={totalRequests}
            setSubmitHandler={setSubmitHandler}
          />
        }
        tableFooter={
          <ActionButtons
            acquiringEntityId={acquiringEntityId}
            matchDataId={matchDataId}
            changeStatusFunction={changeVisaVmssMatchStatusManually}
            navigateTo={'/dashboards/acquiring-entity'}
          />
        }
      />
      <Footer />
    </DashboardLayout>
  );
}

// CHeck if ir have some values of M01 or M02
function containsValues(obj) {
  const values = ['E', 'N'];
  if (obj === null) return false;
  for (let key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      if (Array.isArray(obj[key])) {
        for (let item of obj[key]) {
          if (containsValues(item, values)) return true;
        }
      } else {
        if (containsValues(obj[key], values)) return true;
      }
    } else if (values.includes(obj[key])) {
      return true;
    }
  }
  return false;
}

const getMatchColor = (value) => {
  switch (value) {
    case 'N':
      return 'white';
    case 'P':
      return '#FFD700'; // yellow
    case 'E':
      return 'red';
    default:
      return 'white';
  }
};

function compareMatch(merchantMatch, key) {
  if (merchantMatch?.hasOwnProperty(key)) {
    return getMatchColor(merchantMatch[key]);
  }
  return 'white';
}

export default VisaVmss;
