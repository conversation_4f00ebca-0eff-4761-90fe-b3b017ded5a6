import dayjs from 'dayjs';
import Footer from 'examples/Footer';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import DataTable from 'examples/Tables/DataTable/DataTable';
import { useState } from 'react';
import tableData from './AmlCustomer';
import { getAllCustomers } from 'requests/proxyServer';
import AmlCustimersFilter from 'components/Filters/AmlCustimersFilter/AmlCustimersFilter';

function AmlCustomers() {
  const [tableInfo, setTableInfo] = useState({ ...tableData });

  const [submitHandler, setSubmitHandler] = useState(false);
  const [refreshData, setRefreshData] = useState(false);
  const [sorting, setSorting] = useState([]);

  const [startDate, setStartDate] = useState(undefined);
  const [endDate, setEndDate] = useState(undefined);
  const [filters, setFilters] = useState({ startDate, endDate, customerType: 'Individual' });

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <DataTable
        table={tableInfo}
        canSearch
        submitHandler={submitHandler}
        filters={filters}
        setFilters={setFilters}
        refreshData={refreshData}
        fetchingFunction={getAllCustomers}
        sorting={sorting}
        setSorting={setSorting}
        filtersComponent={
          <AmlCustimersFilter
            filters={filters}
            setFilters={setFilters}
            setSubmitHandler={setSubmitHandler}
            startDate={startDate}
            setStartDate={setStartDate}
            endDate={endDate}
            setEndDate={setEndDate}
            sorting={sorting}
          />
        }
      />
      <Footer />
    </DashboardLayout>
  );
}

export default AmlCustomers;
