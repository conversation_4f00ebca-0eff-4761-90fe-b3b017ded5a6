import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Dialog, DialogActions, DialogContent, Autocomplete } from '@mui/material';
import { toast } from 'react-toastify';

import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDInput from 'components/MDInput';

import { getUserById, updateUser } from 'requests/user';
import { getRoles } from 'requests/roles';

function EditUser() {
  const { userId } = useParams();
  const navigate = useNavigate();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [disabledDialogBtns, setDisabledDialogBtns] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    name: '',
    role: '',
    isBlocked: false
  });
  const [roles, setRoles] = useState([]);
  const toastId = useRef(null);

  const [userData, setUserData] = useState(null);

  // Load user data and roles
  useEffect(() => {
    const loadRoles = async () => {
      try {
        const response = await getRoles();
        if (response?.data?.data) {
          setRoles(response.data.data.map((role) => role.name));
        }
      } catch (error) {
        toast.error(`Error fetching roles: ${error?.response?.data?.message || error.message}`);
      }
    };

    const loadUserData = async () => {
      const result = await fetchUser();
      if (result) {
        setUserData(result);
      }
    };

    loadUserData();
    loadRoles();
  }, []);

  // Set form data when both user data and roles are loaded
  useEffect(() => {
    if (userData && roles.length > 0) {
      const isBlockedValue = 'isBlocked' in userData ? userData.isBlocked : undefined;

      setFormData({
        username: userData.username || '',
        name: userData.name || '',
        role: userData.role?.name || '',
        isBlocked: isBlockedValue
      });
    }
  }, [userData, roles]);

  const fetchUser = async () => {
    try {
      const result = await getUserById(userId);
      if (result?.data) {
        return result.data;
      } else {
        return null;
      }
    } catch (error) {
      toast.error(`Error: ${error?.response?.data?.message}`, {
        className: 'toast-width-600'
      });
      return null;
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleRoleChange = (_, newValue) => {
    setFormData((prevState) => ({
      ...prevState,
      role: newValue
    }));
  };

  const handleIsBlockedChange = (_, newValue) => {
    // Convert string value to boolean
    const isBlockedBool = newValue === 'True';

    setFormData((prevState) => ({
      ...prevState,
      isBlocked: isBlockedBool
    }));
  };

  function handleSubmit(e) {
    e.preventDefault();

    setDisabledDialogBtns(true);
    if (formData) {
      const submissionData = {
        username: formData.username,
        name: formData.name,
        role: formData.role,
        isBlocked: formData.isBlocked
      };

      toastId.current = toast.loading('Loading...');
      updateUser(submissionData, userId)
        .then(() => {
          toast.update(toastId.current, {
            type: 'success',
            render: `User updated successfully.`,
            autoClose: 3000,
            isLoading: false
          });
          navigate('/dashboards/user-management');
        })
        .catch((error) => {
          toast.dismiss(toastId.current);
          let message = '';

          if (error.message.includes('\n')) {
            message = error.message.split('\n');
          } else {
            message = error.message;
          }

          // If error.message is an array, join it with line breaks
          if (Array.isArray(message)) {
            message = (
              <>
                <h3 style={{ color: 'red' }}>Validation Errors</h3>
                {message.map((item, index) => (
                  <p key={index}>{item}</p>
                ))}
              </>
            );
          }

          toast.error(message, {
            className: 'toast-width-600'
          });
        })
        .finally(async () => {
          setDialogOpen(false);
          setDisabledDialogBtns(false);
        });
    }
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDTypography variant='button' fontWeight='medium' color='text'>
        Edit User
      </MDTypography>
      <MDBox
        bgColor='#323a54'
        borderRadius='lg'
        style={{
          padding: '20px'
        }}
      >
        <form>
          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type='text'
              label='Email'
              name='username'
              value={formData.username}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
            />
          </MDBox>

          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type='text'
              label='Full Name'
              name='name'
              value={formData.name}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
            />
          </MDBox>

          <MDBox mb={2}>
            <Autocomplete
              disableClearable
              value={formData.role}
              options={roles}
              onChange={handleRoleChange}
              size='small'
              sx={{ width: '200px' }}
              renderInput={(params) => <MDInput {...params} label='Role' />}
              isOptionEqualToValue={(option, value) => option === value}
            />
          </MDBox>

          <MDBox mb={2}>
            <Autocomplete
              disableClearable
              value={formData.isBlocked === undefined ? '' : formData.isBlocked ? 'True' : 'False'}
              options={['True', 'False']}
              onChange={handleIsBlockedChange}
              size='small'
              sx={{ width: '200px' }}
              renderInput={(params) => <MDInput {...params} label='Is Blocked' />}
              isOptionEqualToValue={(option, value) => option === value}
            />
          </MDBox>

          <MDBox display='flex' justifyContent='center'>
            <MDButton style={{ backgroundColor: 'green' }} variant='outlined' onClick={() => setDialogOpen(true)}>
              Submit
            </MDButton>
          </MDBox>
        </form>
      </MDBox>
      <Dialog open={dialogOpen}>
        <DialogContent>
          <MDTypography variant='h6' color='secondary'>
            Submit Changes!
          </MDTypography>
        </DialogContent>
        <DialogContent>
          <MDTypography variant='paragraph' color='secondary'>
            Are you sure you want to submit changes?
          </MDTypography>
        </DialogContent>
        <DialogActions>
          <MDButton variant='text' color='error' onClick={() => setDialogOpen(false)} disabled={disabledDialogBtns}>
            No
          </MDButton>
          <MDButton variant='text' color='success' onClick={handleSubmit} disabled={disabledDialogBtns}>
            Yes
          </MDButton>
        </DialogActions>
      </Dialog>
      <Footer />
    </DashboardLayout>
  );
}

export default EditUser;
