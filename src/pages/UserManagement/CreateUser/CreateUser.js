import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Autocomplete } from '@mui/material';
import { toast } from 'react-toastify';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import { createUser } from 'requests/user';
import { getRoles } from 'requests/roles';

function CreateUser() {
  const navigate = useNavigate();
  const toastId = useRef(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    role: '',
    name: ''
  });

  const [roles, setRoles] = useState([]);

  useEffect(() => {
    getRoles()
      .then((response) => {
        setRoles(response.data.data.map((role) => role.name));
        setFormData({
          ...formData,
          role: response.data.data[0].name
        });
      })
      .catch((error) => {
        toast.error(`Error fetching roles: ${error?.response?.data?.message || error.message}`);
      });
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleRoleChange = (_, newValue) => {
    setFormData((prevState) => ({
      ...prevState,
      role: newValue
    }));
  };

  function handleSubmit(e) {
    e.preventDefault();
    setIsSubmitting(true);

    toastId.current = toast.loading('Creating user...');
    createUser(formData)
      .then(() => {
        toast.update(toastId.current, {
          type: 'success',
          render: 'User created successfully',
          autoClose: 2000,
          isLoading: false
        });
        navigate('/dashboards/user-management');
      })
      .catch((error) => {
        toast.update(toastId.current, {
          type: 'error',
          render: error.message,
          autoClose: 3000,
          isLoading: false,
          className: 'toast-width-600'
        });
        setIsSubmitting(false);
      });
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDTypography variant='h5' fontWeight='medium' color='text' mb={3}>
        Create New User
      </MDTypography>
      <MDBox
        bgColor='#323a54'
        borderRadius='lg'
        style={{
          padding: '20px'
        }}
      >
        <form onSubmit={handleSubmit}>
          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type='text'
              label='Email'
              name='username'
              value={formData.username}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
              required
            />
          </MDBox>

          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type='text'
              label='Full Name'
              name='name'
              value={formData.name}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
              required
            />
          </MDBox>

          <MDBox mb={3}>
            <Autocomplete
              disableClearable
              value={formData.role}
              options={roles}
              onChange={handleRoleChange}
              size='small'
              sx={{ width: '200px' }}
              renderInput={(params) => <MDInput {...params} label='Role' />}
            />
          </MDBox>

          <MDBox display='flex' gap={2}>
            <MDButton
              variant='outlined'
              color='error'
              onClick={() => navigate('/dashboards/user-management')}
              disabled={isSubmitting}
            >
              Cancel
            </MDButton>
            <MDButton type='submit' variant='gradient' color='success' disabled={isSubmitting}>
              Create User
            </MDButton>
          </MDBox>
        </form>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default CreateUser;
