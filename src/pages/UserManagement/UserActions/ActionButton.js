import { Tooltip, IconButton, Icon } from '@mui/material';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

export function ActionButton(userId, buttonTitle, buttonIcon, onClick) {
  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
      <Tooltip title={buttonTitle}>
        <MDTypography fontSize='0.875rem'>
          <IconButton size='small' color='info' onClick={() => onClick(userId)}>
            <Icon fontSize='small'>{buttonIcon}</Icon>
          </IconButton>
        </MDTypography>
      </Tooltip>
    </MDBox>
  );
}
