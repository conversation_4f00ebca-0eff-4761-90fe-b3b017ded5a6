import { useState, useRef } from 'react';
import { toast } from 'react-toastify';
import MDButton from 'components/MDButton';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import UserManagementFilter from 'components/Filters/UserManagementFilter/UserManagementFilter';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import { getUsers, updateUser, resetPassword } from 'requests/user';
import DataTable from 'examples/Tables/DataTable/DataTable';
import tableData from './UserDataTable';
import RoleManagement from 'components/RoleManagement/RoleManagement';
import { ActionButton } from './UserActions/ActionButton';
import { useNavigate } from 'react-router-dom';

function UserManagement() {
  const navigate = useNavigate();
  const [tableInfo] = useState(tableData);
  const [filters, setFilters] = useState({});
  const [submitHandler, setSubmitHandler] = useState(false);
  const [sorting, setSorting] = useState([{ id: 'isBlocked', desc: false }]);

  const toastId = useRef(null);

  const onEdit = (id) => {
    navigate(`/dashboards/user-management/${id}`);
  };

  const onBlock = async (userId, currentBlockedStatus) => {
    try {
      const newBlockedStatus = !currentBlockedStatus;

      toastId.current = toast.loading(`${newBlockedStatus ? 'Blocking' : 'Unblocking'} user...`);

      // Update the user with the new blocked status
      await updateUser(
        {
          isBlocked: newBlockedStatus
        },
        userId
      );

      toast.update(toastId.current, {
        type: 'success',
        render: `User ${newBlockedStatus ? 'blocked' : 'unblocked'} successfully`,
        autoClose: 2000,
        isLoading: false,
        className: 'toast-width-600'
      });

      setSubmitHandler((prev) => !prev);
    } catch (error) {
      toast.update(toastId.current, {
        type: 'error',
        render: error.message || 'An error occurred',
        autoClose: 3000,
        isLoading: false,
        className: 'toast-width-600'
      });
    }
  };

  const onResetPassword = async (userId) => {
    try {
      // Show loading toast
      toastId.current = toast.loading('Resetting password...');

      // Call the reset password API
      await resetPassword(userId);

      // Show success message
      toast.update(toastId.current, {
        type: 'success',
        render: 'Password reset email sent successfully',
        autoClose: 2000,
        isLoading: false,
        className: 'toast-width-600'
      });
    } catch (error) {
      // Show error message
      toast.update(toastId.current, {
        type: 'error',
        render: error.message || 'An error occurred',
        autoClose: 3000,
        isLoading: false,
        className: 'toast-width-600'
      });
    }
  };

  const transformData = (data) => {
    if (data.length > 0) {
      const rows = data.map((el) => {
        const isBlocked = el.isBlocked === true;
        return {
          ...el,
          action: (
            <MDBox display='flex'>
              <RoleManagement action='update' object='all'>
                {ActionButton(el._id, 'Edit', 'edit', onEdit)}
                {ActionButton(el._id, 'Reset Password', 'lock_reset', onResetPassword)}
                {ActionButton(el._id, isBlocked ? 'Unblock' : 'Block', isBlocked ? 'person' : 'person_off', (id) =>
                  onBlock(id, isBlocked)
                )}
              </RoleManagement>
            </MDBox>
          )
        };
      });
      return rows;
    } else {
      return [];
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={1}>
        <MDTypography variant='h5' fontWeight='medium' color='text'>
          User Management
        </MDTypography>
      </MDBox>
      <MDBox>
        <MDButton
          variant='gradient'
          color='info'
          onClick={() => navigate('/dashboards/user-management/create')}
          sx={{ width: '200px' }}
        >
          Create User
        </MDButton>
      </MDBox>

      <DataTable
        table={tableInfo}
        canSearch
        fetchingFunction={getUsers}
        transformData={transformData}
        submitHandler={submitHandler}
        filters={filters}
        sorting={sorting}
        setSorting={setSorting}
        filtersComponent={
          <UserManagementFilter filters={filters} setFilters={setFilters} setSubmitHandler={setSubmitHandler} />
        }
      />
      <Footer />
    </DashboardLayout>
  );
}

export default UserManagement;
