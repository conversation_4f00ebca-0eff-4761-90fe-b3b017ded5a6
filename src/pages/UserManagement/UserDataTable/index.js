let tableData = {
  columns: [
    {
      header: 'email',
      accessorKey: 'username',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'full name',
      accessorKey: 'name',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Role',
      accessorKey: 'role.name',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Blocked',
      accessorKey: 'isBlocked',
      cell: (info) => (info.getValue() ? 'Yes' : 'No'),
      footer: (props) => props.column.id
    },
    {
      header: 'Action',
      accessorKey: 'action',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    }
  ],
  rows: []
};

export default tableData;
