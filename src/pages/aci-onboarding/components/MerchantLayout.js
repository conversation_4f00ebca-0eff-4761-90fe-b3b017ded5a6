import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDButton from 'components/MDButton';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import React, { useState } from 'react';
import { sendMerchantBulkFile } from 'requests/aci';
import { toast } from 'react-toastify';
import { Button, Dialog, DialogActions, DialogContent } from '@mui/material';
import { Autocomplete } from '@mui/material';
import MDInput from 'components/MDInput';

function MerchantLayout() {
  const fileInputRef = React.useRef(null);

  const [file, setFile] = useState(null);
  const [submitDialog, setSubmitDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const entries = ['Standard', 'Payment Facilitator'];
  const [entry, setEntry] = useState('Standard');

  const loadReportsForCountry = (entry) => setEntry(entry);

  const handleUploadClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) setFile(file);
  };

  const clearFile = () => {
    setFile(null);
    fileInputRef.current.value = '';
  };
  const handleDialog = () => setSubmitDialog(!submitDialog);

  const sendFile = () => {
    const formData = new FormData();
    formData.append('file', file);
    handleDialog();

    const loadingToast = toast.loading('Uploading file...');
    setLoading(true);

    sendMerchantBulkFile(formData, entry)
      .then((response) => {
        toast.dismiss(loadingToast);
        toast.success(response.data.message);
        setLoading(false);
      })
      .catch((error) => {
        toast.dismiss(loadingToast);
        toast.error(error.response?.data?.message ?? error);
        setLoading(false);
      });
  };

  return (
    <MDBox display='block' justifyContent='center' alignItems='center'>
      <MDBox display='inline-flex' flexDirection='column' justifyContent='flex-start' alignItems='start' p={1}>
        <MDTypography variant='h5' color='white' p={1}>
          Upload File
        </MDTypography>

        <MDTypography variant='body2' color='white' px={1} pb={5}>
          Upload an Excel file (.xlsx or .xls format) containing merchant data. <br></br>The file should include
          required merchant information in the specified template format.
        </MDTypography>

        <Autocomplete
          disableClearable
          value={entry}
          options={entries}
          onChange={(event, newValue) => {
            loadReportsForCountry(newValue);
          }}
          size='small'
          sx={{ width: '11rem', ml: 1 }}
          renderInput={(params) => <MDInput {...params} />}
        />

        <MDBox display='flex' py={2} sx={{ ml: 1 }}>
          <input
            type='file'
            ref={fileInputRef}
            accept='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            style={{ display: 'none' }}
            onChange={handleFileChange}
          />
          <MDButton variant='gradient' color='warning' disabled={loading} onClick={handleUploadClick}>
            Upload Document
          </MDButton>

          {file && (
            <MDBox display='flex'>
              <IconButton color='error' sx={{ mt: -0.5 }} onClick={clearFile}>
                <CloseIcon />
              </IconButton>
              <MDTypography sx={{ mr: 2, mt: 0.5 }} variant='body2' color='white'>
                {file.name}
              </MDTypography>
              <MDButton
                variant='outlined'
                color='success'
                size='small'
                sx={{ mt: -0.4 }}
                disabled={loading}
                onClick={handleDialog}
              >
                Submit
              </MDButton>
            </MDBox>
          )}
        </MDBox>
      </MDBox>

      <Dialog open={submitDialog}>
        <DialogContent>
          <MDTypography variant='h6' color='secondary'>
            Send merchants document <i>{file?.name ?? ''}</i>?
          </MDTypography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialog}>Cancel</Button>
          <Button onClick={sendFile}>Send</Button>
        </DialogActions>
      </Dialog>
    </MDBox>
  );
}

export default MerchantLayout;
