import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Dialog, DialogActions, DialogContent } from '@mui/material';
import { toast } from 'react-toastify';

import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';

import RenderObjectInInputs from 'pages/AcquiringEntity/components/RenderObjectInInputs';
import { getAcquiringEntityByIdWithFullDetails, updateAcquiringEntity } from 'requests/acquiringService';

function EditAcquiringEntity() {
  const { acquiringEntityId } = useParams();
  const navigate = useNavigate();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [disabledDialogBtns, setDisabledDialogBtns] = useState(false);
  const [formData, setFormData] = useState(null);
  const toastId = useRef(null);

  useEffect(() => {
    const fetchData = async () => {
      const result = await fetchAcquiringEntityByIdWithFullDetails();
      setFormData(result);
    };

    fetchData();
  }, []);

  const fetchAcquiringEntityByIdWithFullDetails = async () => {
    try {
      const result = await getAcquiringEntityByIdWithFullDetails(acquiringEntityId);
      if (result?.data) {
        return result.data;
      } else {
        return null;
      }
    } catch (error) {
      toast.error(`Error: ${error?.response?.data?.message}`, {
        className: 'toast-width-600'
      });
      return null;
    }
  };

  function updateFormData() {
    const inputValues = {};
    // Get all elements with the class name 'transaction-wrapper-input'
    const wrapperInputs = document.querySelectorAll('.transaction-wrapper-input');
    // Iterate over each wrapper input
    wrapperInputs.forEach((wrapperInput) => {
      // Get all input elements within the wrapper
      const inputs = wrapperInput.querySelectorAll('input, textarea');

      // Iterate over each input element
      inputs.forEach((input) => {
        // Get the attribute name of the input
        const attributeName = input.getAttribute('name');
        // Get the value of the input
        const value = input.value;

        // Skip if name attribute is empty or null
        if (!attributeName) return;

        // Add the input value to the object, even if empty
        // We'll handle empty values when building the nested structure
        inputValues[attributeName] = value;
      });
    });

    let updateFormDataCurrent = {};

    // First pass: build the structure with all values
    for (const key in inputValues) {
      const keys = key.split('.');

      // Access the nested object and update the property
      let nestedObject = updateFormDataCurrent;
      for (let i = 0; i < keys.length - 1; i++) {
        // Create the object path if it doesn't exist
        if (!nestedObject[keys[i]]) {
          // Check if the next key is a number to determine if we need an array
          const isNextKeyNumeric = !isNaN(parseInt(keys[i + 1]));
          nestedObject[keys[i]] = isNextKeyNumeric ? [] : {};
        }
        nestedObject = nestedObject[keys[i]];
      }

      // Because we are getting null key from somewhere :)
      if (keys == 'null') {
        continue;
      }

      // Update the property in the nested object
      const lastKey = keys[keys.length - 1];
      const value = inputValues[key];

      // If the value is empty, set it to null
      if (value === '' || value == null || value == undefined) {
        nestedObject[lastKey] = null;
      } else {
        nestedObject[lastKey] = value;
      }
    }

    // Second pass: clean up arrays to remove empty slots
    cleanupArrays(updateFormDataCurrent);

    updateFormDataCurrent.principalsIds = updateFormDataCurrent.principalsIds.filter((item) => {
      return item !== null && item !== undefined && !(typeof item === 'object' && Object.keys(item).length === 0);
    });

    delete updateFormDataCurrent.visaErrorMatch;
    delete updateFormDataCurrent.mastercardErrorMatch;

    setFormData({ ...updateFormDataCurrent });
    setDialogOpen(true); // Open the dialog after updating form data
  }

  // Helper function to recursively clean up arrays in the object
  function cleanupArrays(obj) {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach((key) => {
      const value = obj[key];

      if (Array.isArray(value)) {
        // Filter out empty, null, or undefined values
        obj[key] = value.filter((item) => item !== '' && item !== null && item !== undefined);

        // Recursively clean up any objects within the array
        obj[key].forEach((item) => {
          if (typeof item === 'object' && item !== null) {
            cleanupArrays(item);
          }
        });
      } else if (typeof value === 'object' && value !== null) {
        // Recursively clean up nested objects
        cleanupArrays(value);
      }
    });
  }

  function handleSubmit(e) {
    e.preventDefault();

    setDisabledDialogBtns(true);
    if (formData) {
      toastId.current = toast.loading('Loading...');
      updateAcquiringEntity(formData)
        .then((res) => {
          updateFormData(null);
          toast.update(toastId.current, {
            type: 'success',
            render: `Acquiring entity updated successfully.`,
            autoClose: 3000,
            isLoading: false
          });
          navigate('/dashboards/acquiring-entity');
        })
        .catch((error) => {
          toast.dismiss(toastId.current);
          let message = '';

          if (error.message.includes('\n')) {
            message = error.message.split('\n');
          } else {
            message = error.message;
          }

          // If error.message is an array, join it with line breaks
          if (Array.isArray(message)) {
            message = (
              <>
                <h3 style={{ color: 'red' }}>Validation Errors</h3>
                {message.map((item, index) => (
                  <p key={index}>{item}</p>
                ))}
              </>
            );
          }

          toast.error(message, {
            className: 'toast-width-600',
            autoClose: false,
            closeOnClick: false
          });
        })
        .finally(async () => {
          setDialogOpen(false);
          setDisabledDialogBtns(false);
        });
    }
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDTypography variant='button' fontWeight='medium' color='text'>
        Edit transaction
      </MDTypography>
      <MDBox
        bgColor='#323a54'
        borderRadius='lg'
        style={{
          padding: '20px'
        }}
      >
        <RenderObjectInInputs formData={formData} />
        <MDBox display='flex' justifyContent='center'>
          <MDButton
            style={{ backgroundColor: 'green' }}
            variant='outlined'
            onClick={() => {
              updateFormData();
            }}
          >
            Submit
          </MDButton>
        </MDBox>
      </MDBox>
      <Dialog open={dialogOpen}>
        <DialogContent>
          <MDTypography variant='h6' color='secondary'>
            Submit Changes!
          </MDTypography>
        </DialogContent>
        <DialogContent>
          <MDTypography variant='paragraph' color='secondary'>
            Are you sure you want to submit changes.
          </MDTypography>
        </DialogContent>
        <DialogActions>
          <MDButton variant='text' color='error' onClick={() => setDialogOpen(false)} disabled={disabledDialogBtns}>
            No
          </MDButton>
          <MDButton variant='text' color='success' onClick={handleSubmit} disabled={disabledDialogBtns}>
            yes
          </MDButton>
        </DialogActions>
      </Dialog>
      <Footer />
    </DashboardLayout>
  );
}

export default EditAcquiringEntity;
