import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, DialogActions, DialogContent } from '@mui/material';
import { toast } from 'react-toastify';

import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';

import CreateEntityRenderInputs from 'pages/AcquiringEntity/components/CreateEntityRenderInputs';
import { createAcquiringEntity } from 'requests/acquiringService';
import { getInitialFormData } from 'pages/AcquiringEntity/constants/formSchema';

function CreateEntity() {
  const navigate = useNavigate();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [disabledDialogBtns, setDisabledDialogBtns] = useState(false);
  const toastId = useRef(null);

  const initialFormData = getInitialFormData();

  const [formData, setFormData] = useState(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to prepare form data before submission
  function prepareFormData() {
    const inputValues = {};
    const wrapperInputs = document.querySelectorAll('.transaction-wrapper-input');

    wrapperInputs.forEach((wrapperInput) => {
      const inputs = wrapperInput.querySelectorAll('input, textarea, select');

      inputs.forEach((input) => {
        const attributeName = input.getAttribute('name');
        const value = input.value;

        if (!attributeName) return;

        inputValues[attributeName] = value;
      });
    });

    let preparedFormData = {};

    // Build the nested structure from flat input values
    for (const key in inputValues) {
      const keys = key.split('.');
      if (keys[0] === 'null') continue;

      let nestedObject = preparedFormData;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!nestedObject[keys[i]]) {
          const isNextKeyNumeric = !isNaN(parseInt(keys[i + 1]));
          nestedObject[keys[i]] = isNextKeyNumeric ? [] : {};
        }
        nestedObject = nestedObject[keys[i]];
      }

      const lastKey = keys[keys.length - 1];
      const value = inputValues[key];

      if (value !== '' && value !== null && value !== undefined) {
        if (value === 'true') {
          nestedObject[lastKey] = true;
        } else if (value === 'false') {
          nestedObject[lastKey] = false;
        } else {
          nestedObject[lastKey] = value;
        }
      }
    }

    cleanupArrays(preparedFormData);
    cleanupEmptyObjects(preparedFormData);

    return preparedFormData;
  }

  function cleanupArrays(obj) {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach((key) => {
      const value = obj[key];

      if (Array.isArray(value)) {
        obj[key] = value.filter((item) => item !== '' && item !== null && item !== undefined);

        obj[key].forEach((item) => {
          if (typeof item === 'object' && item !== null) {
            cleanupArrays(item);
          }
        });

        if (obj[key].length === 0) {
          delete obj[key];
        }
      } else if (typeof value === 'object' && value !== null) {
        cleanupArrays(value);
      }
    });
  }

  function cleanupEmptyObjects(obj) {
    if (!obj || typeof obj !== 'object') return obj;

    Object.keys(obj).forEach((key) => {
      const value = obj[key];

      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            value[index] = cleanupEmptyObjects(item);
          }
        });
        obj[key] = value.filter((item) => {
          if (typeof item === 'object' && item !== null) {
            return Object.keys(item).length > 0;
          }
          return true;
        });

        if (obj[key].length === 0) {
          delete obj[key];
        }
      } else if (typeof value === 'object' && value !== null) {
        obj[key] = cleanupEmptyObjects(value);
        if (Object.keys(obj[key]).length === 0) {
          delete obj[key];
        }
      }
    });

    return obj;
  }

  // Function to validate required fields
  function validateRequiredFields(data) {
    const errors = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!data.registrationNumber || data.registrationNumber.trim() === '') {
      errors.push('Registration Number is required');
    }

    if (!data.phone || (Array.isArray(data.phone) && (!data.phone[0] || data.phone[0].trim() === ''))) {
      errors.push('Phone is required');
    }

    if (!data.country || data.country.trim() === '') {
      errors.push('Country is required');
    }

    if (!data.city || data.city.trim() === '') {
      errors.push('City is required');
    }

    if (!data.zip || data.zip.trim() === '') {
      errors.push('Zip is required');
    }

    if (!data.address1 || data.address1.trim() === '') {
      errors.push('Address1 is required');
    }

    if (!data.email || data.email.trim() === '') {
      errors.push('Email is required');
    }

    // Check principalRawData array for required fields
    if (!data.principalRawData || !Array.isArray(data.principalRawData) || data.principalRawData.length === 0) {
      errors.push('At least one principal is required');
    } else {
      const firstPrincipal = data.principalRawData[0];
      if (!firstPrincipal || typeof firstPrincipal !== 'object') {
        errors.push('Principal information is required');
      } else {
        if (!firstPrincipal.firstName || firstPrincipal.firstName.trim() === '') {
          errors.push('Principal First Name is required');
        }
        if (!firstPrincipal.lastName || firstPrincipal.lastName.trim() === '') {
          errors.push('Principal Last Name is required');
        }
      }
    }

    return errors;
  }

  // Function to open confirmation dialog
  function confirmSubmit() {
    const preparedData = prepareFormData();

    // Validate required fields
    const validationErrors = validateRequiredFields(preparedData);

    if (validationErrors.length > 0) {
      toast.error(
        <>
          <h3 style={{ color: 'red' }}>Required Fields Missing</h3>
          {validationErrors.map((error, index) => (
            <p key={index}>{error}</p>
          ))}
        </>,
        {
          className: 'toast-width-600',
          autoClose: 5000,
          closeOnClick: true
        }
      );
      return;
    }

    setFormData(preparedData);
    setDialogOpen(true);
  }

  // Function to handle form submission
  function handleSubmit(e) {
    e.preventDefault();
    setDisabledDialogBtns(true);
    setIsSubmitting(true);

    toastId.current = toast.loading('Creating entity...');
    createAcquiringEntity(formData)
      .then(() => {
        toast.update(toastId.current, {
          type: 'success',
          render: 'Entity created successfully',
          autoClose: 2000,
          isLoading: false
        });
        navigate('/dashboards/acquiring-entity');
      })
      .catch((error) => {
        toast.dismiss(toastId.current);
        let message = '';

        if (error.message.includes('\n')) {
          message = error.message.split('\n');
        } else {
          message = error.message;
        }

        // If error.message is an array, join it with line breaks
        if (Array.isArray(message)) {
          message = (
            <>
              <h3 style={{ color: 'red' }}>Validation Errors</h3>
              {message.map((item, index) => (
                <p key={index}>{item}</p>
              ))}
            </>
          );
        }

        toast.error(message, {
          className: 'toast-width-600',
          autoClose: false,
          closeOnClick: false
        });
        setIsSubmitting(false);
      })
      .finally(() => {
        setDialogOpen(false);
        setDisabledDialogBtns(false);
      });
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={2}>
        <MDTypography variant='button' fontWeight='medium' color='text'>
          Create Entity
        </MDTypography>
      </MDBox>
      <MDBox
        bgColor='#323a54'
        borderRadius='lg'
        p={3}
        mb={3}
        sx={{
          width: '100%'
        }}
      >
        <form
          onSubmit={(e) => {
            e.preventDefault();
            confirmSubmit();
          }}
        >
          <CreateEntityRenderInputs formData={initialFormData} />
          <MDBox display='flex' gap={2} mt={3} justifyContent='center'>
            <MDButton
              variant='outlined'
              color='error'
              onClick={() => navigate('/dashboards/acquiring-entity')}
              disabled={isSubmitting}
            >
              Cancel
            </MDButton>
            <MDButton type='submit' variant='gradient' color='success' disabled={isSubmitting}>
              Create Entity
            </MDButton>
          </MDBox>
        </form>
      </MDBox>
      <Dialog open={dialogOpen}>
        <DialogContent>
          <MDTypography variant='h6' color='secondary'>
            Create Entity
          </MDTypography>
        </DialogContent>
        <DialogContent>
          <MDTypography variant='paragraph' color='secondary'>
            Are you sure you want to create this entity?
          </MDTypography>
        </DialogContent>
        <DialogActions>
          <MDButton variant='text' color='error' onClick={() => setDialogOpen(false)} disabled={disabledDialogBtns}>
            No
          </MDButton>
          <MDButton variant='text' color='success' onClick={handleSubmit} disabled={disabledDialogBtns}>
            Yes
          </MDButton>
        </DialogActions>
      </Dialog>
      <Footer />
    </DashboardLayout>
  );
}

export default CreateEntity;
