import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import DataTable from 'examples/Tables/DataTable/DataTable';
import tableData from './AcquiringEntityDataTable';
import { useState, useEffect } from 'react';
import Footer from 'examples/Footer';
import { useSearchParams } from 'react-router-dom';
import { getAcquiringEntities } from 'requests/acquiringService';
import MDBox from 'components/MDBox';
import { Icon, IconButton, Tooltip, CircularProgress } from '@mui/material';
import { NavLink, useNavigate } from 'react-router-dom';
import MDTypography from 'components/MDTypography';
import MDButton from 'components/MDButton';
import AcquiringEntityFilter from 'components/Filters/AcquiringEntityFilter/AcquiringEntityFilter';
import MidDialog from './components/dialogs/MidDialog';
import OmnipayDialog from './components/dialogs/OmnipayDialog';
import AciDialog from './components/dialogs/AciDialog';
import DownloadPdfButton from './components/DownloadPdfButton';

function AcquiringEntity() {
  const [tableInfo] = useState(tableData);
  const [searchParams, setSearchParams] = useSearchParams();

  // Helper function to get filters from URL parameters
  const getFiltersFromURL = () => {
    const urlFilters = {};

    const name = searchParams.get('name');
    const registrationNumber = searchParams.get('registrationNumber');

    if (name) urlFilters.name = name;
    if (registrationNumber) urlFilters.registrationNumber = registrationNumber;

    const mastercardMatchStatuses = searchParams.get('mastercardMatchStatuses');
    const visaMatchStatuses = searchParams.get('visaMatchStatuses');
    const sumsubAnswers = searchParams.get('sumsubAnswers');
    const sumsubStatuses = searchParams.get('sumsubStatuses');

    if (mastercardMatchStatuses) {
      urlFilters.mastercardMatchStatuses = mastercardMatchStatuses.split(',');
    }
    if (visaMatchStatuses) {
      urlFilters.visaMatchStatuses = visaMatchStatuses.split(',');
    }
    if (sumsubAnswers) {
      urlFilters.sumsubAnswers = sumsubAnswers.split(',');
    }
    if (sumsubStatuses) {
      urlFilters.sumsubStatuses = sumsubStatuses.split(',');
    }

    return urlFilters;
  };

  const [filters, setFilters] = useState(getFiltersFromURL());
  const [submitHandler, setSubmitHandler] = useState(false);

  // Helper function to update URL with current filters and pagination
  const updateURLWithFilters = (newFilters) => {
    const params = new URLSearchParams(searchParams);

    // Remove existing filter parameters
    params.delete('name');
    params.delete('registrationNumber');
    params.delete('mastercardMatchStatuses');
    params.delete('visaMatchStatuses');
    params.delete('sumsubAnswers');
    params.delete('sumsubStatuses');

    // Set new filter parameters
    if (newFilters.name) {
      params.set('name', newFilters.name);
    }
    if (newFilters.registrationNumber) {
      params.set('registrationNumber', newFilters.registrationNumber);
    }
    if (newFilters.mastercardMatchStatuses && newFilters.mastercardMatchStatuses.length > 0) {
      params.set('mastercardMatchStatuses', newFilters.mastercardMatchStatuses.join(','));
    }
    if (newFilters.visaMatchStatuses && newFilters.visaMatchStatuses.length > 0) {
      params.set('visaMatchStatuses', newFilters.visaMatchStatuses.join(','));
    }
    if (newFilters.sumsubAnswers && newFilters.sumsubAnswers.length > 0) {
      params.set('sumsubAnswers', newFilters.sumsubAnswers.join(','));
    }
    if (newFilters.sumsubStatuses && newFilters.sumsubStatuses.length > 0) {
      params.set('sumsubStatuses', newFilters.sumsubStatuses.join(','));
    }

    setSearchParams(params);
  };

  // Update URL when filters change
  useEffect(() => {
    updateURLWithFilters(filters);
  }, [filters]);
  const [openDialog, setOpenDialog] = useState({ mid: false, omnipay: false, aci: false });
  const [acquiringEntity, setAcquiringEntity] = useState(null);
  const [parentMerchantId, setParentMerchantId] = useState('');
  const [pspNumber, setPspNumber] = useState('');

  const [sorting, setSorting] = useState([{ id: 'createdAt', desc: true }]); // Default sort: newest on top
  const navigate = useNavigate();

  // Helper function to determine match status based on entity data
  const getMatchStatus = (acquireEntity, typeUrl) => {
    let isThereAnyFailedRequest = false;
    let resolvedFromRyvylAcquiring = null;
    let isMatch = null;
    let isRecordMissing = false;

    if (typeUrl === 'mastercard') {
      if (!acquireEntity?.mastercardMatchData || acquireEntity?.mastercardMatchData?.length <= 0) {
        isRecordMissing = true;
      }
      isThereAnyFailedRequest = acquireEntity?.mastercardMatchData?.failedRequest > 0 ? true : false;
      resolvedFromRyvylAcquiring = acquireEntity?.mastercardMatchData?.resolvedMastercardMatch ?? null;
      isMatch = acquireEntity?.mastercardMatchData?.isThereAnyMatch;
    } else {
      if (!acquireEntity?.visaMatchData || acquireEntity?.visaMatchData?.length <= 0) {
        isRecordMissing = true;
      }
      isThereAnyFailedRequest = acquireEntity?.visaMatchData?.failedRequest > 0 ? true : false;
      resolvedFromRyvylAcquiring = acquireEntity?.visaMatchData?.resolvedVisaMatch ?? null;
      isMatch = acquireEntity?.visaMatchData?.isThereAnyMatch;
    }

    if (isRecordMissing) {
      return 'In Progress';
    } else if (isThereAnyFailedRequest) {
      return 'Failed Requests';
    } else if (resolvedFromRyvylAcquiring === true) {
      return 'Resolved (Approved)';
    } else if (resolvedFromRyvylAcquiring === false) {
      return 'Resolved (Rejected)';
    } else if (isMatch === true) {
      return 'Match';
    } else if (isMatch === false) {
      return 'No Match';
    } else {
      return 'Error';
    }
  };

  const handleDialog = (type, acquiringEntity) => {
    setOpenDialog((prev) => ({ ...prev, [type]: !prev[type] }));

    // A workaround to fix components being shown during the transition of the dialog
    setTimeout(
      () => {
        setAcquiringEntity(acquiringEntity);
      },
      acquiringEntity ? 0 : 200
    );

    if (type === 'mid') {
      setParentMerchantId(!acquiringEntity?.pspIsoReferralAgentName ? '20103000' : '');
      setPspNumber('');
    }
  };

  const transformData = (data) => {
    if (data.length > 0) {
      const rows = data.map((el, index) => {
        const mastercardMatchStatus = getMatchStatus(el, 'mastercard');
        const visaMatchStatus = getMatchStatus(el, 'visa');

        return {
          ...el,
          rowIndex: index + 1,
          mastercardMatch: matchButton({ acquireEntity: el, typeUrl: 'mastercard', navigate }),
          visaMatch: matchButton({ acquireEntity: el, typeUrl: 'visa', navigate }),
          mastercardMatchStatus,
          visaMatchStatus,
          sumsubAnswer: el?.sumsub?.reviewAnswer ?? 'No answer',
          sumsubStatus: el?.sumsub?.reviewStatus ?? 'No status',
          sumsubAnswer: el?.sumsub?.reviewAnswer ?? (
            <MDTypography fontSize='0.875rem' color='white'>
              No answer
            </MDTypography>
          ),
          sumsubStatus: el?.sumsub?.reviewStatus ?? (
            <MDTypography fontSize='0.875rem' color='white'>
              No status
            </MDTypography>
          ),
          midConfig: <MDBox display='flex'>{configureMid(el)}</MDBox>,
          omnipayStatus: <MDBox display='flex'>{onboardingResult(el, 'omnipay')}</MDBox>,
          aciStatus: <MDBox display='flex'>{onboardingResult(el, 'aci')}</MDBox>,
          export: <MDBox display='flex'>{exportButton(el._id)}</MDBox>,
          action: <MDBox display='flex'>{editButton(el._id)}</MDBox>
        };
      });
      return rows;
    } else {
      return [];
    }
  };

  function configureMid(acquiringEntity) {
    return (
      <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }} mt={-0.5}>
        <Tooltip title='Configure MID'>
          <NavLink onClick={() => handleDialog('mid', acquiringEntity)}>
            <MDTypography fontSize='0.875rem'>
              <IconButton
                size='small'
                color={
                  acquiringEntity.midConfiguration
                    ? acquiringEntity.midConfiguration.error
                      ? 'error'
                      : 'success'
                    : 'secondary'
                }
              >
                <Icon fontSize='small'>badge</Icon>
              </IconButton>
            </MDTypography>
          </NavLink>
        </Tooltip>
      </MDBox>
    );
  }

  function onboardingResult(acquiringEntity, type) {
    const result = type === 'omnipay' ? acquiringEntity.omnipayResult : acquiringEntity.aciResult;
    const properties = {
      icon: result ? (result.error ? 'remove_circle' : 'check_circle') : 'radio_button_unchecked',
      color: result ? (result.error ? 'error' : 'success') : 'secondary',
      text: result ? (result.error ? 'Failed' : 'Onboarded') : 'No status'
    };
    return (
      <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
        <Tooltip title={type === 'omnipay' ? 'OmniPay Status' : 'ACI Status'}>
          <NavLink onClick={() => handleDialog(type, acquiringEntity)}>
            <IconButton size='small'>
              <MDBox display='flex' alignItems='center'>
                <Icon fontSize='small' color={properties.color}>
                  {properties.icon}
                </Icon>
                <MDTypography ml={1} fontSize='0.875rem' color='white'>
                  {properties.text}
                </MDTypography>
              </MDBox>
            </IconButton>
          </NavLink>
        </Tooltip>
      </MDBox>
    );
  }

  function editButton(acquiringEntityId) {
    return (
      <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
        <Tooltip title='Edit'>
          <NavLink to={`/dashboards/acquiring-entity/edit/${acquiringEntityId}`}>
            <MDTypography fontSize='0.875rem'>
              <IconButton size='small' color='info'>
                <Icon fontSize='small'>edit</Icon>
              </IconButton>
            </MDTypography>
          </NavLink>
        </Tooltip>
      </MDBox>
    );
  }

  function exportButton(acquiringEntityId) {
    return (
      <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
        <Tooltip title='Export Files'>
          <MDTypography fontSize='0.875rem'>
            <IconButton
              size='small'
              color='success'
              onClick={() => navigate(`/dashboards/acquiring-entity/files/${acquiringEntityId}`)}
            >
              <Icon fontSize='small'>download</Icon>
            </IconButton>
          </MDTypography>
        </Tooltip>
      </MDBox>
    );
  }

  function matchButton({ acquireEntity, typeUrl, navigate }) {
    const acquiringEntityId = acquireEntity._id;
    let isThereAnyFailedRequest = false;
    let resolvedFromRyvylAcquiring = null;
    let isMatch = null;
    let isRecordMissing = false;
    let requestError = '';

    if (typeUrl === 'mastercard') {
      if (!acquireEntity?.mastercardMatchData || acquireEntity?.mastercardMatchData?.length <= 0) {
        isRecordMissing = true;
      }
      isThereAnyFailedRequest = acquireEntity?.mastercardMatchData?.failedRequest > 0 ? true : false;
      resolvedFromRyvylAcquiring = acquireEntity?.mastercardMatchData?.resolvedMastercardMatch ?? null;
      isMatch = acquireEntity?.mastercardMatchData?.isThereAnyMatch;
      requestError = acquireEntity?.mastercardMatchData?.errorMessageRequest;
    } else {
      if (!acquireEntity?.visaMatchData || acquireEntity?.visaMatchData?.length <= 0) {
        isRecordMissing = true;
      }
      isThereAnyFailedRequest = acquireEntity?.visaMatchData?.failedRequest > 0 ? true : false;
      resolvedFromRyvylAcquiring = acquireEntity?.visaMatchData?.resolvedVisaMatch ?? null;
      isMatch = acquireEntity?.visaMatchData?.isThereAnyMatch;
      requestError = acquireEntity?.visaMatchData?.errorMessageRequest;
    }

    let iconMatch = null;
    let buttonText = null;
    let color = '#53db53';

    let title = '';

    if (isRecordMissing) {
      buttonText = 'Check In Porgress';
      iconMatch = 'close';
      color = 'red';
    } else if (isThereAnyFailedRequest) {
      buttonText = `Failed requests ${
        typeUrl === 'mastercard'
          ? acquireEntity?.mastercardMatchData?.failedRequest
          : acquireEntity?.visaMatchData?.failedRequest
      } of ${
        typeUrl === 'mastercard'
          ? acquireEntity?.mastercardMatchData?.requestMadeToCheckMerchant
          : acquireEntity?.visaMatchData?.requestMadeToCheckMerchant
      }`;
      iconMatch = 'close';
      color = 'red';
      title = requestError;
    } else if (resolvedFromRyvylAcquiring === true) {
      buttonText = 'Resolved by Ryvyl';
      iconMatch = 'thumb_up';
    } else if (resolvedFromRyvylAcquiring === false) {
      buttonText = 'Rejected by Ryvyl';
      iconMatch = 'thumb_down';
      color = 'red';
    } else if (isMatch === true) {
      buttonText = 'Match';
      iconMatch = 'close';
      color = 'red';
    } else if (isMatch === false) {
      buttonText = 'No Match';
      iconMatch = 'check';
    } else {
      buttonText = 'Error on request';
      iconMatch = 'close';
      color = 'red';
      title = requestError;
    }

    const path = typeUrl === 'mastercard' ? 'mastercard-match-pro-references' : 'visa-vmss';
    const href = `/dashboards/${path}/${acquiringEntityId}`;

    return (
      <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
        <Tooltip title={title}>
          <MDTypography fontSize='0.875rem'>
            <a
              href={href}
              onClick={(e) => {
                e.preventDefault();
                navigate(href);
              }}
              style={{ textDecoration: 'none' }}
            >
              <IconButton size='small' color='info' variant='text'>
                <MDBox display='flex' alignItems='center' gap={1}>
                  <Icon sx={{ color: color }} fontSize='small'>
                    {iconMatch}
                  </Icon>
                  <MDTypography ml={1} sx={{ color: color }} fontSize='0.875rem'>
                    {buttonText}
                  </MDTypography>
                </MDBox>
              </IconButton>
            </a>
          </MDTypography>
        </Tooltip>
        <DownloadPdfButton acquiringEntityId={acquiringEntityId} type={typeUrl} />
      </MDBox>
    );
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={1}>
        <MDTypography variant='h5' fontWeight='medium' color='text'>
          Acquiring Entity Management
        </MDTypography>
      </MDBox>
      <MDBox mb={3}>
        <MDButton
          variant='gradient'
          color='info'
          onClick={() => navigate('/dashboards/acquiring-entity/create')}
          sx={{ width: '200px' }}
        >
          Create Entity
        </MDButton>
      </MDBox>
      <DataTable
        table={tableInfo}
        canSearch
        fetchingFunction={getAcquiringEntities}
        transformData={transformData}
        submitHandler={submitHandler}
        filters={filters}
        sorting={sorting}
        setSorting={setSorting}
        filtersComponent={
          <AcquiringEntityFilter filters={filters} setFilters={setFilters} setSubmitHandler={setSubmitHandler} />
        }
      />
      <MidDialog
        openDialog={openDialog.mid}
        acquiringEntity={acquiringEntity}
        parentMerchantId={parentMerchantId}
        setParentMerchantId={setParentMerchantId}
        setPspNumber={setPspNumber}
        pspNumber={pspNumber}
        handleDialog={handleDialog}
        setSubmitHandler={setSubmitHandler}
      />
      <OmnipayDialog openDialog={openDialog.omnipay} acquiringEntity={acquiringEntity} handleDialog={handleDialog} />
      <AciDialog openDialog={openDialog.aci} acquiringEntity={acquiringEntity} handleDialog={handleDialog} />
      <Footer />
    </DashboardLayout>
  );
}

export default AcquiringEntity;
