import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

// @mui material components
import { Card, CardContent, IconButton, Tooltip, CircularProgress } from '@mui/material';
import { Download, InsertDriveFile } from '@mui/icons-material';

// Material Dashboard 2 PRO React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// Material Dashboard 2 PRO React examples
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

// API functions
import { getAcquiringEntityFiles, downloadAcquiringEntityFile } from 'requests/acquiringService';

function AcquiringEntityFiles() {
  const { entityId } = useParams();
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [downloadingFiles, setDownloadingFiles] = useState(new Set());

  useEffect(() => {
    fetchFiles();
  }, [entityId]);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await getAcquiringEntityFiles(entityId);
      if (response?.data) {
        setFiles(response.data || []);
      } else {
        setFiles([]);
      }
    } catch (error) {
      toast.error(`Error fetching files: ${error.message}`, {
        className: 'toast-width-600'
      });
      setFiles([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (file) => {
    try {
      setDownloadingFiles((prev) => new Set([...prev, file.id]));

      await downloadAcquiringEntityFile(entityId, file.id, file.filename);

      toast.success(`File ${file.filename} downloaded successfully`, {
        className: 'toast-width-600'
      });
    } catch (error) {
      toast.error(`Error downloading file: ${error.message}`, {
        className: 'toast-width-600'
      });
    } finally {
      setDownloadingFiles((prev) => {
        const newSet = new Set(prev);
        newSet.delete(file.id);
        return newSet;
      });
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />

      <MDBox mb={3}>
        <MDTypography variant='body2' color='text' mt={1}>
          Download available files for this acquiring entity
        </MDTypography>
      </MDBox>

      {loading ? (
        <MDBox display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
          <CircularProgress color='info' />
        </MDBox>
      ) : (
        <MDBox>
          {files.length === 0 ? (
            <Card>
              <CardContent>
                <MDBox textAlign='center' py={4}>
                  <InsertDriveFile sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <MDTypography variant='h6' color='text' mb={1}>
                    No Files Available
                  </MDTypography>
                  <MDTypography variant='body2' color='text'>
                    There are no files available for download for this entity.
                  </MDTypography>
                </MDBox>
              </CardContent>
            </Card>
          ) : (
            <MDBox display='grid' gridTemplateColumns='repeat(auto-fill, minmax(350px, 1fr))' gap={3}>
              {files.map((file) => (
                <Card key={file.filename} sx={{ height: 'fit-content' }}>
                  <CardContent>
                    <MDBox display='flex' alignItems='flex-start' gap={2} mb={2}>
                      <MDBox flex={1} sx={{ minWidth: 0, overflow: 'hidden' }}>
                        <Tooltip title={file.id} placement='top'>
                          <MDTypography
                            variant='h6'
                            fontWeight='medium'
                            color='text'
                            sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              display: 'block'
                            }}
                          >
                            {file.id}
                          </MDTypography>
                        </Tooltip>
                        <MDTypography variant='caption' color='text'>
                          {file.filename || 'Unknown Type'}
                        </MDTypography>
                      </MDBox>

                      <MDBox sx={{ flexShrink: 0 }}>
                        <Tooltip title='Download File'>
                          <IconButton
                            color='info'
                            onClick={() => handleDownload(file)}
                            disabled={downloadingFiles.has(file.id)}
                            size='small'
                          >
                            {downloadingFiles.has(file.id) ? <CircularProgress size={20} /> : <Download />}
                          </IconButton>
                        </Tooltip>
                      </MDBox>
                    </MDBox>
                  </CardContent>
                </Card>
              ))}
            </MDBox>
          )}
        </MDBox>
      )}
    </DashboardLayout>
  );
}

export default AcquiringEntityFiles;
