import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, IconButton } from '@mui/material';
import { Close } from '@mui/icons-material';

// Material Dashboard 2 PRO React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDButton from 'components/MDButton';

function AgreementInfoDialog({ open, onClose, agreementData }) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth='md' fullWidth>
      <DialogTitle>
        <MDBox display='flex' justifyContent='space-between' alignItems='center'>
          <MDTypography variant='h5' fontWeight='medium'>
            Prefilled Agreement Form Information
          </MDTypography>
          <IconButton onClick={onClose} size='small'>
            <Close />
          </IconButton>
        </MDBox>
      </DialogTitle>

      <DialogContent>
        <MDBox py={2}>
          {agreementData?.data ? (
            <MDBox>
              {/* URL Section */}
              <MDBox mb={3}>
                <MDTypography variant='h6' fontWeight='medium' mb={1}>
                  URL Link to Prefilled Agreement Form:
                </MDTypography>
                <MDBox
                  display='flex'
                  alignItems='center'
                  gap={1}
                  p={2}
                  sx={{
                    backgroundColor: 'grey.100',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.300'
                  }}
                >
                  <MDTypography
                    variant='body2'
                    sx={{
                      wordBreak: 'break-all',
                      flex: 1,
                      fontFamily: 'monospace',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    {agreementData.data.url}
                  </MDTypography>
                </MDBox>
              </MDBox>

              {agreementData.data.email && (
                <MDBox mb={3}>
                  <MDTypography variant='h5' fontWeight='bold' mb={2} color='info.main'>
                    Signatory Director and Authorized Representative
                  </MDTypography>
                  <MDTypography variant='h6' fontWeight='medium' mb={1}>
                    Email:
                  </MDTypography>
                  <MDBox
                    p={2}
                    sx={{
                      backgroundColor: 'grey.100',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.300'
                    }}
                  >
                    <MDTypography variant='body2'>{agreementData.data.email}</MDTypography>
                  </MDBox>

                  {agreementData.data.name && (
                    <MDBox mt={2}>
                      <MDTypography variant='h6' fontWeight='medium' mb={1}>
                        Name:
                      </MDTypography>
                      <MDBox
                        p={2}
                        sx={{
                          backgroundColor: 'grey.100',
                          borderRadius: 1,
                          border: '1px solid',
                          borderColor: 'grey.300'
                        }}
                      >
                        <MDTypography variant='body2'>{agreementData.data.name}</MDTypography>
                      </MDBox>
                    </MDBox>
                  )}
                </MDBox>
              )}
            </MDBox>
          ) : (
            <MDBox textAlign='center' py={4}>
              <MDTypography variant='body1' color='text.secondary'>
                No agreement information available
              </MDTypography>
            </MDBox>
          )}
        </MDBox>
      </DialogContent>

      <DialogActions>
        <MDButton variant='outlined' color='secondary' onClick={onClose}>
          Close
        </MDButton>
      </DialogActions>
    </Dialog>
  );
}

export default AgreementInfoDialog;
