import React, { useState } from 'react';
import { Dialog, DialogActions, DialogContent } from '@mui/material';
import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';
import { Icon } from '@mui/material';
import { sendMidConfiguration, resetMidConfiguration } from 'requests/acquiringService';
import { toast } from 'react-toastify';

function MidDialog({
  openDialog,
  acquiringEntity,
  parentMerchantId,
  setParentMerchantId,
  pspNumber,
  setPspNumber,
  handleDialog,
  setSubmitHandler
}) {
  const [isInvalidMID, setIsInvalidMID] = useState(false);
  const [isInvalidPspNumber, setIsInvalidPspNumber] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const midConfigUi = () => {
    return (
      <>
        <MDTypography variant='h6' color='white' fontWeight='medium' mb={2}>
          {acquiringEntity?.mid ? '' : 'Verify the following submission of MID configuration below.'}
        </MDTypography>
        <MDTypography variant='body2' color='white' fontSize='0.80rem' lineHeight={1.2}>
          {acquiringEntity?.mid
            ? ''
            : 'Configuration provided from the merchant application. Modify the required fields and submit.'}
        </MDTypography>
        <MDInput
          disableClearable
          disabled
          value={acquiringEntity?.entityType ?? 'Merchant'}
          label='Who is fullfilling this form?'
          size='small'
          sx={{
            width: '100%',
            mt: 3,
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px'
            }
          }}
        />
        <MDInput
          disabled
          value={acquiringEntity?.name}
          label='Company Name'
          size='small'
          sx={{
            width: '100%',
            mt: 3,
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px'
            }
          }}
        />

        {acquiringEntity?.pspIsoReferralAgentName && (
          <MDInput
            disabled
            value={acquiringEntity?.pspIsoReferralAgentName}
            label={'PSP/Referral Agent'}
            size='small'
            sx={{
              width: '100%',
              mt: 3,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '8px'
              }
            }}
          />
        )}

        <MDInput
          required
          type='text'
          margin='dense'
          value={parentMerchantId}
          inputProps={{ maxLength: 8 }}
          onChange={(e) => setParentMerchantId(e.target.value)}
          label='Parent Merchant Identifier'
          size='small'
          error={isInvalidMID}
          sx={{
            width: '100%',
            mt: 3,
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px'
            }
          }}
        />

        <MDTypography variant='caption' color='error' sx={{ display: isInvalidMID ? 'block' : 'none' }}>
          Parent MID must be 8 digits long.
        </MDTypography>

        <MDInput
          required
          type='text'
          margin='dense'
          value={pspNumber}
          inputProps={{ maxLength: 3 }}
          onChange={(e) => setPspNumber(e.target.value)}
          label='PSP Number'
          size='small'
          error={isInvalidPspNumber}
          sx={{
            width: '100%',
            mt: 3,
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px'
            }
          }}
        />
        <MDTypography variant='caption' color='error' sx={{ display: isInvalidPspNumber ? 'block' : 'none' }}>
          PSP number must be 3 digits long.
        </MDTypography>
      </>
    );
  };

  const midInformation = () => {
    const midConfig = acquiringEntity?.midConfiguration;

    return (
      <>
        <MDTypography variant='h6' color='white' fontWeight='medium' mb={1.5}>
          Merchant Terminal Information
        </MDTypography>

        <MDBox
          display='grid'
          gridTemplateColumns='repeat(3, 1fr)'
          gridTemplateRows='auto auto'
          gap={1.5}
          mt={1}
          sx={{
            '& > div': {
              p: 1,
              borderRadius: '6px',
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              boxShadow: '0 1px 4px 0 rgba(0,0,0,0.1)',
              transition: 'all 0.2s ease-in-out',
              border: '1px solid rgba(255, 255, 255, 0.06)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.08)',
                transform: 'translateY(-1px)',
                boxShadow: '0 2px 8px 0 rgba(0,0,0,0.12)'
              }
            }
          }}
        >
          {/* First row */}
          <MDBox gridColumn='span 2'>
            <MDTypography
              variant='caption'
              color='info'
              fontWeight='bold'
              textTransform='uppercase'
              letterSpacing={0.2}
            >
              Company Name
            </MDTypography>
            <MDTypography variant='body2' color='white' mt={0.5} fontWeight='medium'>
              {acquiringEntity?.name || '-'}
            </MDTypography>
          </MDBox>

          <MDBox>
            <MDTypography
              variant='caption'
              color='info'
              fontWeight='bold'
              textTransform='uppercase'
              letterSpacing={0.2}
            >
              Master MID
            </MDTypography>
            <MDTypography variant='body2' color='white' mt={0.5} fontWeight='medium'>
              {midConfig?.isMasterMid ? (
                <MDBox component='span' sx={{ color: '#4caf50', display: 'flex', alignItems: 'center', gap: 0.3 }}>
                  <Icon fontSize='small'>check_circle</Icon> Yes
                </MDBox>
              ) : (
                <MDBox component='span' sx={{ color: '#ff9800', display: 'flex', alignItems: 'center', gap: 0.3 }}>
                  <Icon fontSize='small'>remove_circle</Icon> No
                </MDBox>
              )}
            </MDTypography>
          </MDBox>

          {/* Second row */}

          <MDBox gridColumn='span 2'>
            <MDTypography
              variant='caption'
              color='info'
              fontWeight='bold'
              textTransform='uppercase'
              letterSpacing={0.2}
            >
              Long Terminal
            </MDTypography>
            <MDTypography variant='body2' color='white' mt={0.5} fontWeight='medium'>
              {midConfig?.longTerminal || '-'}
            </MDTypography>
          </MDBox>

          <MDBox>
            <MDTypography
              variant='caption'
              color='info'
              fontWeight='bold'
              textTransform='uppercase'
              letterSpacing={0.2}
            >
              Parent Long Terminal
            </MDTypography>
            <MDTypography variant='body2' color='white' mt={0.5} fontWeight='medium'>
              {midConfig?.parentLongTerminal || '-'}
            </MDTypography>
          </MDBox>
        </MDBox>

        {midConfig?.error && (
          <MDBox
            mt={1.5}
            p={1.5}
            borderRadius='6px'
            sx={{
              backgroundColor: 'rgba(255, 0, 0, 0.08)',
              border: '1px solid rgba(244, 67, 54, 0.2)',
              boxShadow: '0 1px 4px 0 rgba(244, 67, 54, 0.08)'
            }}
          >
            <MDTypography
              variant='caption'
              color='error'
              fontWeight='bold'
              textTransform='uppercase'
              letterSpacing={0.2}
            >
              <MDBox component='span' sx={{ display: 'flex', alignItems: 'center', gap: 0.3 }}>
                <Icon fontSize='small'>error</Icon> Error
              </MDBox>
            </MDTypography>
            <MDTypography variant='body2' color='error' mt={0.5}>
              {midConfig.error}
            </MDTypography>
          </MDBox>
        )}
      </>
    );
  };

  const handleConfirm = () => {
    // Clearing all characters that are not digits
    // Expecting 3 digits for pspNumber and 8 digits for parentMerchantId
    const cleanedPspNumber = pspNumber.replace(/\D/g, '');
    const cleanedParentMerchantId = parentMerchantId.replace(/\D/g, '');

    setIsInvalidPspNumber(cleanedPspNumber.length != 3);
    setIsInvalidMID(cleanedParentMerchantId.length !== 8);

    if (cleanedPspNumber.length !== 3 || cleanedParentMerchantId.length !== 8) return;

    setIsSubmitting(true);

    sendMidConfiguration(acquiringEntity, parentMerchantId, pspNumber)
      .then((res) => {
        toast.success(res.data.message);
        setIsSubmitting(false);
        handleDialog('mid', null);
      })
      .catch((error) => {
        toast.error(error.message);
        setIsSubmitting(false);
      });
  };

  const handleReset = () => {
    setIsSubmitting(true);

    resetMidConfiguration(acquiringEntity)
      .then((res) => {
        toast.success(res.data.message);
        setIsSubmitting(false);
        setSubmitHandler((prev) => !prev);
        handleDialog('mid', null);
      })
      .catch((error) => {
        toast.error(error.message);
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <Dialog open={openDialog} maxWidth='sm' fullWidth>
        <DialogContent
          sx={{
            background: ({ functions: { linearGradient }, palette: { gradients } }) =>
              linearGradient(gradients.dark.main, gradients.dark.state, 180),
            color: 'white',
            padding: 3
          }}
        >
          {!acquiringEntity?.midConfiguration ? midConfigUi() : midInformation()}
        </DialogContent>
        <DialogActions
          sx={{
            padding: 2,
            background: ({ palette }) => palette.background.default,
            justifyContent: 'flex-end',
            gap: 1
          }}
        >
          <MDButton
            disabled={isSubmitting}
            onClick={() => handleDialog('mid', null)}
            variant='outlined'
            color='secondary'
          >
            Cancel
          </MDButton>
          <MDButton
            disabled={isSubmitting}
            variant='contained'
            color='info'
            onClick={handleConfirm}
            sx={{ display: acquiringEntity?.midConfiguration ? 'none' : 'block' }}
          >
            {isSubmitting ? 'Configuring...' : 'Confirm'}
          </MDButton>
          <MDButton
            disabled={isSubmitting}
            variant='contained'
            color='info'
            onClick={handleReset}
            sx={{ display: acquiringEntity?.midConfiguration?.error ? 'block' : 'none' }}
          >
            {isSubmitting ? 'Resetting...' : 'Reset'}
          </MDButton>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default MidDialog;
