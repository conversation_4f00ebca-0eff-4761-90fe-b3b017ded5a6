import React from 'react';
import { Dialog, DialogActions, DialogContent } from '@mui/material';
import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import { Icon } from '@mui/material';
import { retryOnboarding } from 'requests/acquiringService';
import { toast } from 'react-toastify';

function AciDialog({ openDialog, acquiringEntity, handleDialog }) {
  const aciResult = acquiringEntity?.aciResult;

  const handleRetry = () => {
    // Implement retry logic here
    retryOnboarding('aci', acquiringEntity._id)
      .then((res) => {
        toast.success(res.data.message);
        handleDialog('aci', null);
      })
      .catch((error) => {
        toast.error(error.message);
      });
  };

  return (
    <Dialog open={openDialog} maxWidth='sm' fullWidth>
      <DialogContent
        sx={{
          background: ({ functions: { linearGradient }, palette: { gradients } }) =>
            linearGradient(gradients.dark.main, gradients.dark.state, 180),
          color: 'white',
          padding: 3
        }}
      >
        <MDTypography variant='h6' color='white' fontWeight='medium' mb={1.5}>
          ACI Status Details
        </MDTypography>

        {aciResult ? (
          <MDBox mt={2}>
            <MDBox
              display='grid'
              gridTemplateColumns='1fr'
              gap={1.5}
              mt={1}
              sx={{
                '& > div': {
                  p: 1,
                  borderRadius: '6px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  boxShadow: '0 1px 4px 0 rgba(0,0,0,0.1)',
                  transition: 'all 0.2s ease-in-out',
                  border: '1px solid rgba(255, 255, 255, 0.06)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.08)',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 2px 8px 0 rgba(0,0,0,0.12)'
                  }
                }
              }}
            >
              <MDBox>
                <MDTypography
                  variant='caption'
                  color='info'
                  fontWeight='bold'
                  textTransform='uppercase'
                  letterSpacing={0.2}
                >
                  Status
                </MDTypography>
                <MDTypography variant='body2' color='white' mt={0.5} fontWeight='medium'>
                  {aciResult.error ? (
                    <MDBox component='span' sx={{ color: '#f44336', display: 'flex', alignItems: 'center', gap: 0.3 }}>
                      <Icon fontSize='small'>error</Icon> Failed
                    </MDBox>
                  ) : (
                    <MDBox component='span' sx={{ color: '#4caf50', display: 'flex', alignItems: 'center', gap: 0.3 }}>
                      <Icon fontSize='small'>check_circle</Icon> Onboarded
                    </MDBox>
                  )}
                </MDTypography>
              </MDBox>
            </MDBox>

            {/* Onboarded Entities Section */}
            {aciResult.onboardedEntities && aciResult.onboardedEntities.length > 0 && (
              <MDBox mt={2}>
                <MDTypography
                  variant='caption'
                  color='info'
                  fontWeight='bold'
                  textTransform='uppercase'
                  letterSpacing={0.2}
                  sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                >
                  Onboarded Entities
                </MDTypography>
                <MDBox
                  mt={1}
                  sx={{
                    maxHeight: '200px',
                    overflowY: 'auto',
                    backgroundColor: 'rgba(255, 255, 255, 0.03)',
                    borderRadius: '6px',
                    p: 1,
                    '&::-webkit-scrollbar': {
                      width: '8px'
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      borderRadius: '4px'
                    },
                    '&:hover::-webkit-scrollbar-thumb': {
                      backgroundColor: 'rgba(255, 255, 255, 0.3)'
                    }
                  }}
                >
                  {aciResult.onboardedEntities.map((entity, index) => (
                    <MDBox
                      key={index}
                      mb={1}
                      p={1}
                      borderRadius='4px'
                      sx={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                    >
                      <MDTypography variant='body2' color='white' fontWeight='medium'>
                        Terminal: {entity.longTerminal}
                      </MDTypography>
                      <MDTypography variant='caption' color='white'>
                        Website: {entity.url}
                      </MDTypography>
                      <br />
                      <MDTypography variant='caption' color='white'>
                        Currency: {entity.currency}
                      </MDTypography>
                    </MDBox>
                  ))}
                </MDBox>
              </MDBox>
            )}

            {/* Error Message Section */}
            {aciResult.error && (
              <MDBox
                mt={1.5}
                p={1.5}
                borderRadius='6px'
                sx={{
                  backgroundColor: 'rgba(255, 0, 0, 0.08)',
                  border: '1px solid rgba(244, 67, 54, 0.2)',
                  boxShadow: '0 1px 4px 0 rgba(244, 67, 54, 0.08)'
                }}
              >
                <MDTypography
                  variant='caption'
                  color='error'
                  fontWeight='bold'
                  textTransform='uppercase'
                  letterSpacing={0.2}
                >
                  <MDBox component='span' sx={{ display: 'flex', alignItems: 'center', gap: 0.3 }}>
                    <Icon fontSize='small'>error</Icon> Error
                  </MDBox>
                </MDTypography>
                <MDTypography variant='body2' color='error' mt={0.5}>
                  {aciResult.error}
                </MDTypography>
              </MDBox>
            )}
          </MDBox>
        ) : (
          <MDTypography variant='body2' color='white'>
            No ACI status information available.
          </MDTypography>
        )}
      </DialogContent>
      <DialogActions
        sx={{
          padding: 2,
          background: ({ palette }) => palette.background.default,
          justifyContent: 'flex-end',
          gap: 1
        }}
      >
        <MDButton onClick={() => handleDialog('aci')} variant='outlined' color='secondary'>
          Close
        </MDButton>
        <MDButton
          onClick={handleRetry}
          variant='contained'
          color='info'
          sx={{ display: acquiringEntity?.aciResult?.error ? 'block' : 'none' }}
        >
          Retry
        </MDButton>
      </DialogActions>
    </Dialog>
  );
}

export default AciDialog;
