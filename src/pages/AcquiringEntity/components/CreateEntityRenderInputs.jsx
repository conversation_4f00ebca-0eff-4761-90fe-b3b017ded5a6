import React, { useState, useCallback } from 'react';
import { Autocomplete, IconButton } from '@mui/material';
import Icon from '@mui/material/Icon';
import MDBox from 'components/MDBox';
import MDInput from 'components/MDInput';
import MDTypography from 'components/MDTypography';
import MDButton from 'components/MDButton';

// Create an uncontrolled input component that preserves values during scrolling
const UncontrolledInput = React.memo(({ name, defaultValue, type = 'text', required = false, ...props }) => {
  return <MDInput name={name} defaultValue={defaultValue || ''} type={type} required={required} {...props} />;
});

function CreateEntityRenderInputs({ formData }) {
  const [localFormData, setLocalFormData] = useState(formData);
  const [booleanValues, setBooleanValues] = useState({});

  // List of known boolean field names (including nested paths and array-based paths)
  const booleanFields = [
    'tradeOverInternet',
    'additionalData.integrationsRequirements.mandatory3ds',
    // Array-based boolean fields
    'nativeTokensSold',
    'tools',
    'withdrawals',
    'risks',
    'training',
    'enhancedDueDiligence',
    'preventionSystem',
    'cryptoInvolvement',
    'contentConsentAgreements',
    'liveStreamingControl',
    'potentiallyIllegalContentComplaintProcess',
    'illegalContentReporting',
    'consentAgreementsRetention',
    'offerAIContent',
    'confirmNoAIDeepfakes',
    'highValueKYC',
    'insurance',
    'highValuePlayerKYC',
    'payoutReserves',
    'bonusAbusePrevention',
    'selfExclusionOrResponsibleTools',
    'coolingOff',
    'highValueGoods',
    'gamblingInclusion',
    'childProtection',
    'ageVerification',
    'guestOrAnnonymousAccounts',
    'spendingLimits',
    'tieredAccountSystem'
  ];

  if (!localFormData || Object.keys(localFormData).length === 0) {
    return <MDBox>Loading...</MDBox>;
  }

  // Helper function to get nested value from object path
  const getNestedValue = useCallback((obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }, []);

  // Helper function to check if a field should be treated as boolean
  const isBooleanField = useCallback(
    (path) => {
      // Extract the field name from the path (last part after the last dot)
      const fieldName = path.split('.').pop();
      return booleanFields.includes(path) || booleanFields.includes(fieldName);
    },
    [booleanFields]
  );

  // Helper function to create a deep copy of an object/array
  const deepClone = useCallback((obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (Array.isArray(obj)) return obj.map(deepClone);
    return Object.keys(obj).reduce((copy, key) => {
      copy[key] = deepClone(obj[key]);
      return copy;
    }, {});
  }, []);

  // Handler for adding array items
  const handleAddArrayItem = useCallback(
    (path) => {
      const currentArray = getNestedValue(localFormData, path);
      if (Array.isArray(currentArray)) {
        const newFormData = deepClone(localFormData);
        const arrayToUpdate = getNestedValue(newFormData, path);

        // Create a new item based on the structure of existing items
        let newItem;
        if (arrayToUpdate.length > 0) {
          const firstItem = arrayToUpdate[0];
          if (typeof firstItem === 'object' && firstItem !== null) {
            // For complex objects, create a copy with empty values
            newItem = deepClone(firstItem);
            const clearValues = (obj) => {
              Object.keys(obj).forEach((key) => {
                if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                  clearValues(obj[key]);
                } else if (Array.isArray(obj[key])) {
                  obj[key] = [''];
                } else if (typeof obj[key] === 'boolean') {
                  obj[key] = null;
                } else {
                  obj[key] = '';
                }
              });
            };
            clearValues(newItem);
          } else {
            newItem = '';
          }
        } else {
          newItem = '';
        }

        arrayToUpdate.push(newItem);
        setLocalFormData(newFormData);
      }
    },
    [localFormData, getNestedValue, deepClone]
  );

  // Handler for removing array items
  const handleRemoveArrayItem = useCallback(
    (path, index) => {
      const currentArray = getNestedValue(localFormData, path);
      if (Array.isArray(currentArray) && currentArray.length > 1) {
        const newFormData = deepClone(localFormData);
        const arrayToUpdate = getNestedValue(newFormData, path);
        arrayToUpdate.splice(index, 1);
        setLocalFormData(newFormData);
      }
    },
    [localFormData, getNestedValue, deepClone]
  );

  // Handler for boolean changes
  const handleBooleanChange = useCallback((path, value) => {
    // Update local state for immediate UI feedback
    setBooleanValues((prev) => ({ ...prev, [path]: value }));

    // Update the hidden input that will be used for form submission
    const hiddenInput = document.querySelector(`input[name="${path}"]`);
    if (hiddenInput) {
      hiddenInput.value = value;
    }
  }, []);

  // Function to check if a field is required
  const isRequiredField = (key, path) => {
    const fullPath = path ? `${path}.${key}` : key;
    return (
      fullPath === 'name' ||
      fullPath === 'registrationNumber' ||
      fullPath === 'phone' ||
      fullPath === 'country' ||
      fullPath === 'city' ||
      fullPath === 'zip' ||
      fullPath === 'address1' ||
      fullPath === 'email' ||
      fullPath === 'principalRawData.0.firstName' ||
      fullPath === 'principalRawData.0.lastName' ||
      fullPath === 'principalRawData.0.phone' ||
      fullPath === 'principalRawData.0.email' ||
      fullPath === 'principalRawData.0.country' ||
      fullPath === 'principalRawData.0.city' ||
      fullPath === 'principalRawData.0.zip' ||
      fullPath === 'principalRawData.0.address1'
    );
  };

  // Function to determine input type based on field name
  const getInputType = (key, value) => {
    const fieldName = key.toLowerCase();
    if (fieldName.includes('email')) {
      return 'email';
    }
    if (typeof value === 'number') {
      return 'number';
    }
    return 'text';
  };

  // Function to render a field based on its type and value
  const renderField = (key, value, path = '') => {
    const fullPath = path ? `${path}.${key}` : key;
    const isRequired = isRequiredField(key, path);

    // Handle arrays
    if (Array.isArray(value)) {
      return (
        <MDBox key={fullPath} bgColor='#212843' p={2} borderRadius='lg' mb={2} className='transaction-wrapper-input'>
          <MDBox display='flex' justifyContent='space-between' alignItems='center' mb={2}>
            <MDTypography variant='button' fontWeight='medium' color='text'>
              {key}
            </MDTypography>
            <MDButton color='info' variant='outlined' size='small' onClick={() => handleAddArrayItem(fullPath)}>
              Add Item
            </MDButton>
          </MDBox>

          {localFormData &&
            getNestedValue(localFormData, fullPath)?.map((item, index) => {
              const itemPath = `${fullPath}.${index}`;

              if (typeof item === 'object' && item !== null) {
                // Render complex object in array
                return (
                  <MDBox
                    key={itemPath}
                    mb={2}
                    p={2}
                    bgColor='#323a54'
                    borderRadius='lg'
                    className='transaction-wrapper-input'
                  >
                    <MDBox display='flex' justifyContent='space-between' alignItems='center' mb={2}>
                      <MDTypography variant='caption' color='text'>
                        {key.replace(/s$/, '')} {index + 1}
                      </MDTypography>
                      {getNestedValue(localFormData, fullPath)?.length > 1 && (
                        <IconButton color='error' size='small' onClick={() => handleRemoveArrayItem(fullPath, index)}>
                          <Icon>delete</Icon>
                        </IconButton>
                      )}
                    </MDBox>
                    {Object.entries(item).map(([itemKey, itemValue]) => renderField(itemKey, itemValue, itemPath))}
                  </MDBox>
                );
              } else {
                // Render simple value in array
                return (
                  <MDBox
                    key={itemPath}
                    display='flex'
                    alignItems='center'
                    gap={1}
                    mb={1}
                    className='transaction-wrapper-input'
                  >
                    <UncontrolledInput
                      name={itemPath}
                      defaultValue={item || ''}
                      sx={{
                        flex: 1,
                        '& .MuiInputBase-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          borderRadius: '8px'
                        }
                      }}
                    />
                    {getNestedValue(localFormData, fullPath)?.length > 1 && (
                      <IconButton color='error' size='small' onClick={() => handleRemoveArrayItem(fullPath, index)}>
                        <Icon>delete</Icon>
                      </IconButton>
                    )}
                  </MDBox>
                );
              }
            })}
        </MDBox>
      );
    }

    // Handle objects
    if (typeof value === 'object' && value !== null) {
      return (
        <MDBox key={fullPath} bgColor='#39425f' p={2} borderRadius='lg' mb={2} className='transaction-wrapper-input'>
          <MDTypography variant='button' fontWeight='medium' color='text' mb={2}>
            {key}
          </MDTypography>
          <MDBox display='flex' flexDirection='column' gap={2}>
            {Object.entries(value).map(([nestedKey, nestedValue]) => renderField(nestedKey, nestedValue, fullPath))}
          </MDBox>
        </MDBox>
      );
    }

    // Handle boolean values
    if (typeof value === 'boolean' || (value === null && isBooleanField(fullPath))) {
      return (
        <MDBox
          key={fullPath}
          display='flex'
          justifyContent='space-between'
          alignItems='center'
          bgColor='#212843'
          p={2}
          borderRadius='lg'
          mb={2}
          className='transaction-wrapper-input'
        >
          <MDTypography variant='button' fontWeight='medium' color='text'>
            {key}
            {isRequired && <span style={{ color: 'red', marginLeft: '4px' }}>*</span>}
          </MDTypography>
          <MDBox sx={{ width: '50%' }}>
            <Autocomplete
              disableClearable
              value={
                booleanValues[fullPath] !== undefined ? booleanValues[fullPath] : value === null ? '' : value.toString()
              }
              options={['', 'true', 'false']}
              getOptionLabel={(option) => {
                if (option === '') return '';
                if (option === 'true') return 'True';
                if (option === 'false') return 'False';
                return option;
              }}
              onChange={(_, newValue) => handleBooleanChange(fullPath, newValue)}
              size='small'
              renderInput={(params) => (
                <MDInput
                  {...params}
                  placeholder='Select value'
                  sx={{
                    '& .MuiInputBase-root': {
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      borderRadius: '8px'
                    }
                  }}
                />
              )}
            />
            <input
              type='hidden'
              name={fullPath}
              defaultValue={
                booleanValues[fullPath] !== undefined ? booleanValues[fullPath] : value === null ? '' : value.toString()
              }
              className='transaction-wrapper-input'
            />
          </MDBox>
        </MDBox>
      );
    }

    // Handle simple values (strings, numbers)
    return (
      <MDBox
        key={fullPath}
        display='flex'
        justifyContent='space-between'
        alignItems='center'
        bgColor='#212843'
        p={2}
        borderRadius='lg'
        mb={2}
        className='transaction-wrapper-input'
      >
        <MDTypography variant='button' fontWeight='medium' color='text'>
          {key}
          {isRequired && <span style={{ color: 'red', marginLeft: '4px' }}>*</span>}
        </MDTypography>
        <UncontrolledInput
          name={fullPath}
          defaultValue={value || ''}
          type={getInputType(key, value)}
          required={isRequired}
          sx={{
            width: '50%',
            '& .MuiInputBase-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px'
            }
          }}
        />
      </MDBox>
    );
  };

  return <MDBox>{Object.entries(localFormData).map(([key, value]) => renderField(key, value))}</MDBox>;
}

export default React.memo(CreateEntityRenderInputs);
