import { useState } from 'react';
import { toast } from 'react-toastify';
import { Icon, CircularProgress, Tooltip, IconButton } from '@mui/material';
import MDButton from 'components/MDButton';
import { downloadMastercardMatchProPdf, downloadVisaVmssPdf } from 'requests/acquiringService';
import MDBox from 'components/MDBox';

function DownloadPdfButton({ acquiringEntityId, type }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    const loadingToast = toast.loading('Generating PDF report... Please wait.', {
      className: 'toast-width-600'
    });
    try {
      setIsLoading(true);

      const response =
        type === 'mastercard'
          ? await downloadMastercardMatchProPdf(acquiringEntityId)
          : await downloadVisaVmssPdf(acquiringEntityId);

      if (response?.base64Content && response?.filename) {
        // Convert base64 to blob
        const byteCharacters = atob(response.base64Content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = response.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        setTimeout(() => {
          toast.dismiss(loadingToast);
        }, 300);
      } else {
        throw new Error('Invalid PDF data received');
      }
    } catch (error) {
      toast.dismiss(loadingToast);

      toast.error(error.message || 'Failed to generate PDF report', {
        className: 'toast-width-600',
        autoClose: 4000,
        closeOnClick: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
      <Tooltip title='Download PDF'>
        <IconButton size='small' color='info' onClick={handleDownload} disabled={isLoading}>
          {isLoading ? <CircularProgress size={20} color='inherit' /> : <Icon fontSize='small'>picture_as_pdf</Icon>}
        </IconButton>
      </Tooltip>
    </MDBox>
  );
}

export default DownloadPdfButton;
