import React, { useCallback, useMemo, useState, useReducer, useEffect } from 'react';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import MDInput from 'components/MDInput';
import { getPrincipalVales } from 'requests/acquiringService';
import { toast } from 'react-toastify';

// Helper function to get the template structure for specific paths (arrays and objects)
const getItemTemplate = (path) => {
  const templates = {
    'additionalData.cryptoSettlement': {
      currency: '',
      percentage: '',
      walletAddress: '',
      preferredNetwork: '',
      cryptocurrency: ''
    },
    'additionalData.bankSettlement': {
      currency: '',
      bankCountry: '',
      bankName: '',
      bankIban: '',
      bankCode: '',
      bankAccountHolder: ''
    },
    'additionalData.targetMarketsAndDistribution': {
      country: '',
      volumePercentage: ''
    },

    'additionalData.businessOffering.industryBusinessCategory': {
      category: '',
      subcategory: ['']
    },

    'additionalData.settlementAccountMetrics.billingMethods': '',
    'additionalData.settlementAccountMetrics.fraudAndChargebacksRates': [''],

    websites: {
      url: '',
      statementDescriptor: '',
      cityField: '',
      isWebsiteClassified: false,
      mccClassification: ''
    },
    // Principal data templates
    principalRawData: {
      firstName: '',
      middleName: '',
      lastName: '',
      email: '',
      phone: '',
      country: '',
      state: '',
      city: '',
      zip: '',
      address1: '',
      address2: '',
      dateOfBirth: '',
      passportNumber: '',
      driverLicenseNumber: '',
      positionInCompany: ''
    },

    'additionalData.cryptoIndustry': {
      cryptoActivityType: [''],
      cryptocurrenciesList: [''],
      nativeTokensSold: null,
      nativeTokensTypes: [''],
      customerBase: [''],
      conversions: [''],
      KYCStages: [''],
      tools: null,
      withdrawals: null,
      withdrawalsWalletTypes: [''],
      withdrawalsProcess: '',
      risks: null,
      yearsEngaging: '',
      training: null,
      pastTransactions: '',
      professionalBackground: ''
    },
    'additionalData.MSBIndustry': {
      employeesNumber: '',
      annualTurnover: '',
      serviceTypes: [''],
      customerBase: [''],
      internationalTransactionsRatio: '',
      enhancedDueDiligence: null,
      KYCStages: [''],
      customerIDVerification: [''],
      preventionSystem: null,
      complianceEmployeesNumber: '',
      highRiskClientsRatio: '',
      complianceAudits: '',
      cryptoInvolvement: null,
      cryptoExposureDetails: ''
    },
    'additionalData.adultServices': {
      contentSources: [''],
      contentConsentAgreements: null,
      contentVerificationMethods: '',
      contentPoviderAgeAndIDVerification: '',
      contentReviewProcess: '',
      liveStreamingControl: null,
      liveStreamingMonitoringAndRemoval: '',
      removalAppealProcess: '',
      marketingAndSearchTerms: '',
      potentiallyIllegalContentPreventionProcess: '',
      potentiallyIllegalContentComplaintProcess: null,
      illegalContentReporting: null,
      traffickingAndAbusePolicy: '',
      consentAgreementsRetention: null,
      offerAIContent: null,
      AIContentSources: '',
      AIContentAgreements: '',
      confirmNoAIDeepfakes: null
    },
    'additionalData.datingEscortData': {
      antiTraffickingProgram: '',
      contentReviewProcess: '',
      contentRemovalAppeals: '',
      marketingAndSearchTermsCompliance: '',
      contentProviderConsentsDepiction: '',
      consentPublicDistribution: '',
      consentContentDownload: '',
      consentsValidationAndStorage: '',
      evidenceValidationAndRetention: '',
      marketingStrategy: '',
      fakeBotPrevention: ''
    },
    'additionalData.industrySpecificStatement': {
      highValueKYC: null,
      insurance: null,
      highValuePlayerKYC: null,
      payoutReserves: null,
      bonusAbusePrevention: null,
      selfExclusionOrResponsibleTools: null,
      coolingOff: null,
      highValueGoods: null,
      gamblingInclusion: null,
      childProtection: null,
      ageVerification: null,
      guestOrAnnonymousAccounts: null,
      spendingLimits: null,
      tieredAccountSystem: null,
      kycStages: '',
      forexLicenseHolder: '',
      gamblingLicense: '',
      geofencing: '',
      fraudPrevention: '',
      minimumAgeRequirements: '',
      payoutWithdrawals: '',
      cardRevenueRatio: '',
      suspiciousPatternsPrevention: '',
      massBettingPrevention: '',
      singleOrMultiVendor: '',
      transacitonProcessing: '',
      disputeResolution: '',
      vendorsNumber: '',
      suspiciousAccountHandling: '',
      payoutReserveManagement: '',
      selfExclusionOrResponsibleToolsDescription: '',
      consumerProtectionMethods: [''],
      marketplaceTypes: [''],
      vendorOnboarding: [''],
      gamingServiceTypes: [''],
      currencyTypes: [''],
      transactionTypes: [''],
      accountCreationInformation: [''],
      fraudPreventionGmg: [''],
      digWalTypes: [''],
      serviceTypesDigWal: [''],
      targetAudience: [''],
      onboardingInformation: [''],
      IDVerification: ['']
    },

    'additionalData.currencies': '',
    'additionalData.settlementCurrencies': '',

    'additionalData.integrationsRequirements': {
      integrationType: '',
      pciComplianceStatus: '',
      thirdPartyGateway: '',
      integrationOption: '',
      paymentPageHostingPreference: '',
      paymentPageURL: '',
      mandatory3ds: null
    },
    'additionalData.salesMetrics': {
      totalMonthlySalesVolume: '',
      expectedMonthlyVolume: '',
      numberOfExpectedMonthlyTransactions: '',
      averageTransactionAmount: '',
      highestTransactionAmount: '',
      lowestTransactionAmount: ''
    },
    'additionalData.settlementAccountMetrics': {
      monthlySalesVolume: '',
      estimatedMonthlyTransfers: '',
      billingMethods: [''],
      processedCreditCardsBefore: '',
      fraudAndChargebacksRates: [['']],
      isRevenueFromBusinessActivity: '',
      freeTrialZeroAmount: ''
    }
  };

  return templates[path] || null;
};

const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (Array.isArray(obj)) return obj.map(deepClone);
  return Object.keys(obj).reduce((copy, key) => {
    copy[key] = deepClone(obj[key]);
    return copy;
  }, {});
};

const propertiesReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_PROPERTY':
      const { path, isArray } = action.payload;
      const pathProps = state[path] || [];

      if (isArray) {
        // For arrays
        const currentArray = action.payload.currentArray || [];
        const originalArrayLength = currentArray.length;
        const nextIndex = originalArrayLength + pathProps.length;

        // Default case for other arrays
        return {
          ...state,
          [path]: [...pathProps, { index: nextIndex, value: '' }]
        };
      } else {
        return state;
      }

    case 'UPDATE_PROPERTY':
      const { path: updatePath, index, field, value } = action.payload;

      if (state[updatePath][index][field] === value) return state;

      const updatedProps = [...state[updatePath]];
      updatedProps[index] = { ...updatedProps[index], [field]: value };

      return {
        ...state,
        [updatePath]: updatedProps
      };

    default:
      return state;
  }
};

const InputField = React.memo(
  ({ initialValue = '', onBlur, placeholder, style, multiline = false, maxRows = 1, name = '' }) => {
    const [localValue, setLocalValue] = useState(initialValue);

    const handleChange = (e) => {
      setLocalValue(e.target.value);
    };

    const handleBlur = () => {
      if (onBlur && localValue !== initialValue) {
        onBlur(localValue);
      }
    };

    useEffect(() => {
      setLocalValue(initialValue);
    }, [initialValue]);

    return (
      <MDInput
        className='fast-input'
        placeholder={placeholder}
        value={localValue}
        onChange={handleChange}
        onBlur={handleBlur}
        multiline={multiline}
        maxRows={maxRows}
        style={style}
        name={name}
      />
    );
  }
);

// Create a separate component for new properties
const NewPropertyInputs = React.memo(({ path, isArray, properties, onPropertyChange }) => {
  return (
    <>
      {properties.map((prop, idx) => {
        if (isArray) {
          return (
            <MDBox key={`new-array-item-${path}-${idx}`}>
              <MDTypography variant='button' fontWeight='medium' color='text'>
                New Item {prop.index}
              </MDTypography>
              <MDBox
                width='100%'
                mt={{ xs: 1, sm: 0 }}
                display='flex'
                justifyContent='flex-end'
                className='transaction-wrapper-input'
              >
                <InputField
                  initialValue={prop.value}
                  onBlur={(newValue) => onPropertyChange(path, idx, 'value', newValue)}
                  multiline={true}
                  maxRows={10}
                  style={{ width: '300px' }}
                  name={`${path}.${prop.index}`}
                />
              </MDBox>
            </MDBox>
          );
        } else {
          return null;
        }
      })}
    </>
  );
});

function RenderObjectInInputs({ formData, readOnlyData, requiredData }) {
  const doNotShowProperties = ['__v', 'resolved', '_id'];
  const priorityOrder = ['name', 'registrationNumber'];

  const [newProperties, dispatch] = useReducer(propertiesReducer, {});

  const [change, setChange] = useState(false);

  const getValueByPath = useCallback((obj, path) => {
    if (!obj || !path) return null;

    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined) return null;
      current = current[key];
    }

    return current;
  }, []);

  // Memoized callback for property changes
  const handleNewPropertyChange = useCallback((path, index, field, value) => {
    dispatch({
      type: 'UPDATE_PROPERTY',
      payload: { path, index, field, value }
    });
  }, []);

  // Helper function to get or create a nested array in an object using a path string
  const getOrCreateArrayByPath = useCallback((obj, path) => {
    const keys = path.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    const lastKey = keys[keys.length - 1];
    if (!current[lastKey]) {
      current[lastKey] = [];
    }

    return current[lastKey];
  }, []);

  // Memoized function to handle adding a new property
  const handleAddProperty = useCallback(
    async (path, isArray) => {
      const currentArray = isArray ? getValueByPath(formData, path) : null;

      if (path == 'principalsIds') {
        try {
          const defaultPrincipalData = await getPrincipalVales();
          const defaultPrincipal = defaultPrincipalData.data;
          if (!defaultPrincipal) {
            toast.error('No principal values found.');
            return;
          }
          setChange((prev) => !prev);
          formData.principalsIds.push(defaultPrincipal);
        } catch (error) {
          toast.error('Error fetching principal values');
        }

        return;
      }

      const template = getItemTemplate(path);

      if (template) {
        if (isArray) {
          // For complex objects with known structure, add directly to formData array
          const targetArray = getOrCreateArrayByPath(formData, path);
          const newItem = deepClone(template);
          targetArray.push(newItem);
          setChange((prev) => !prev);
        } else {
          // For complex objects (non-arrays), create the object structure
          const keys = path.split('.');
          let current = formData;

          for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!current[key] || typeof current[key] !== 'object') {
              current[key] = {};
            }
            current = current[key];
          }

          const lastKey = keys[keys.length - 1];
          if (!current[lastKey]) {
            current[lastKey] = deepClone(template);
            setChange((prev) => !prev);
          }
        }
      } else {
        // For simple arrays or unknown structures, use the reducer
        dispatch({
          type: 'ADD_PROPERTY',
          payload: { path, isArray, currentArray }
        });
      }
    },
    [formData, getValueByPath, getOrCreateArrayByPath]
  );

  // Function to handle the close/open button
  const handleCloseButton = useCallback((e) => {
    const closeWindow = e.target.closest('.wrapper-object-window').querySelector('.close-object-window');

    if (closeWindow) {
      const button = e.target;
      const parentWrapper = button.closest('.wrapper-object-window');
      const expandCollapseAllBtn = parentWrapper.querySelector('.expand-collapse-all-btn');

      if (button.textContent === 'Open') {
        button.textContent = 'Close';
        closeWindow.style.opacity = 1;
        closeWindow.style.height = 'auto';
        if (expandCollapseAllBtn) {
          expandCollapseAllBtn.style.display = 'inline-flex';
        }
      } else {
        closeWindow.style.opacity = 0;
        closeWindow.style.height = '0px';
        button.textContent = 'Open';
        if (expandCollapseAllBtn) {
          expandCollapseAllBtn.style.display = 'none';
          expandCollapseAllBtn.textContent = 'Expand All';
        }
      }
    }
  }, []);

  const handleExpandCollapseAll = useCallback((e) => {
    const button = e.target;
    const parentWrapper = button.closest('.wrapper-object-window');
    const allChildWrappers = parentWrapper.querySelectorAll('.wrapper-object-window .wrapper-object-window');

    const isExpanding = button.textContent === 'Expand All';

    allChildWrappers.forEach((wrapper) => {
      const closeWindow = wrapper.querySelector('.close-object-window');
      const toggleButton = wrapper.querySelector('button[data-toggle-btn="true"]');
      const expandCollapseControls = closeWindow?.querySelector('.expand-collapse-controls');

      if (closeWindow && toggleButton) {
        if (isExpanding) {
          toggleButton.textContent = 'Close';
          closeWindow.style.opacity = 1;
          closeWindow.style.height = 'auto';
          if (expandCollapseControls) {
            expandCollapseControls.style.opacity = 1;
          }
        } else {
          toggleButton.textContent = 'Open';
          closeWindow.style.opacity = 0;
          closeWindow.style.height = '0px';
          if (expandCollapseControls) {
            expandCollapseControls.style.opacity = 0;
          }
        }
      }
    });

    button.textContent = isExpanding ? 'Collapse All' : 'Expand All';
  }, []);

  // Memoized render function to prevent unnecessary re-renders
  const renderInputs = useMemo(() => {
    const recursiveRender = (originalData, prefix = '') => {
      const data = { ...originalData };
      if (!data) {
        return <></>;
      }

      // Helper function to render priority fields
      const renderPriorityField = (key, value, path, index) => {
        const color = path.split('.').length % 2 === 0 ? '#39425f' : '#212843';
        const stringValue = typeof value === 'string' ? value : JSON.stringify(value, null, 2);

        return (
          <MDBox
            key={`priority-${path}-${index}`}
            display='flex'
            justifyContent='space-between'
            alignItems={{ xs: 'flex-start', sm: 'center' }}
            style={{
              background: color,
              padding: '10px',
              marginTop: '8px',
              marginBottom: '8px'
            }}
          >
            <MDTypography variant='button' fontWeight='medium' color='text'>
              {key}
            </MDTypography>
            <MDBox width='100%' mt={{ xs: 1, sm: 0 }} display='flex' justifyContent='flex-end'>
              <MDInput
                className='transaction-wrapper-input'
                display='flex'
                value={stringValue}
                multiline
                minRows={3}
                maxRows={10}
                style={{ width: '500px' }}
                sx={{
                  background: '#292727',
                  color: 'white',
                  borderRadius: '10px',
                  '& .MuiInputBase-root': {
                    maxHeight: '300px',
                    overflow: 'auto !important'
                  }
                }}
                name={path}
              />
            </MDBox>
          </MDBox>
        );
      };

      const disabledElements = [];
      const regularElements = [];
      const priorityElements = [];

      // First, handle priority elements in the specified order
      priorityOrder.forEach((priorityKey) => {
        if (data.hasOwnProperty(priorityKey)) {
          const value = data[priorityKey];
          const randomIndex = Math.random().toString(36).substring(2);
          priorityElements.push(renderRegularElement(priorityKey, value, randomIndex));

          delete data[priorityKey];
        }
      });

      // Then handle remaining elements as before
      Object.entries(data).forEach(([key, value], index) => {
        renderRegularElement(key, value, index);
      });

      function renderRegularElement(key, value, index) {
        const path = prefix ? `${prefix}.${key}` : key;
        const isReadOnlyPath = checkPathInList(path, readOnlyData);

        const isRequiredField = checkPathInList(path, requiredData);

        const color = path.split('.').length % 2 === 0 ? '#39425f' : '#212843';
        const isSubObjectHasNotShowProperties = doNotShowProperties.some(
          (prop) => path.endsWith(`.${prop}`) || doNotShowProperties.includes(path)
        );

        if (typeof value === 'object' && value !== null) {
          const isArray = Array.isArray(value);

          const childObjectCount = isArray
            ? value.filter((item) => typeof item === 'object' && item !== null).length
            : Object.values(value).filter((val) => typeof val === 'object' && val !== null).length;

          const hasMultipleChildren = childObjectCount > 3;

          regularElements.push(
            <MDBox
              key={`wrapper-${path}-${index}`}
              className='wrapper-object-window'
              display='flex'
              flexDirection='column'
              justifyContent='space-between'
              alignItems={{ xs: 'flex-start', sm: 'center' }}
              style={{
                background: color,
                padding: '10px',
                marginBottom: '10px'
              }}
            >
              <MDBox display='flex' justifyContent='space-between' alignItems='center' width='100%'>
                <MDTypography variant='button' fontWeight='medium' color='text'>
                  {key} {isArray ? '(Array)' : '(Object)'}
                </MDTypography>
                <MDBox display='flex' gap={1}>
                  {isArray && (
                    <MDButton
                      color='info'
                      variant='outlined'
                      size='small'
                      onClick={() => handleAddProperty(path, isArray)}
                    >
                      Add Value
                    </MDButton>
                  )}
                  {hasMultipleChildren && (
                    <MDButton
                      color='secondary'
                      variant='outlined'
                      size='small'
                      onClick={handleExpandCollapseAll}
                      style={{ display: 'none' }}
                      className='expand-collapse-all-btn'
                    >
                      Expand All
                    </MDButton>
                  )}
                  <MDButton color='light' variant='outlined' onClick={handleCloseButton} data-toggle-btn='true'>
                    Open
                  </MDButton>
                </MDBox>
              </MDBox>

              <MDBox
                className='close-object-window'
                display='flex'
                flexDirection='column'
                width='100%'
                height='0px'
                style={{
                  paddingLeft: '10px',
                  opacity: 0
                }}
              >
                {recursiveRender(value, path)}

                {/* Use the optimized component for new properties */}
                {newProperties[path] && (
                  <NewPropertyInputs
                    path={path}
                    isArray={isArray}
                    properties={newProperties[path]}
                    onPropertyChange={handleNewPropertyChange}
                  />
                )}
              </MDBox>
            </MDBox>
          );
        } else {
          regularElements.push(
            <MDBox
              key={`element-${path}-${index}`}
              display={isSubObjectHasNotShowProperties ? 'none' : 'flex'}
              justifyContent='space-between'
              alignItems={{ xs: 'flex-start', sm: 'center' }}
              style={{
                background: color,
                padding: '10px',
                marginTop: '8px',
                marginBottom: '8px'
              }}
            >
              <MDTypography variant='button' fontWeight='medium' color='text'>
                {key}
                {isRequiredField && <span style={{ color: 'red', marginLeft: 4 }}>*</span>}
              </MDTypography>
              <MDBox width='100%' mt={{ xs: 1, sm: 0 }} display='flex' justifyContent='flex-end'>
                <MDInput
                  className={`transaction-wrapper-input ${isReadOnlyPath ? 'read-only-input' : ''}`}
                  display='flex'
                  defaultValue={formData[path] || value}
                  multiline
                  maxRows={10}
                  readOnly
                  inputProps={
                    isReadOnlyPath
                      ? {
                          readOnly: true,
                          'aria-readonly': true
                        }
                      : {}
                  }
                  style={{
                    width: '300px'
                  }}
                  sx={{
                    '&.read-only-input .MuiInputBase-root': {
                      backgroundColor: 'rgba(0, 128, 255, 0.1)',
                      color: '#66b3ff',
                      opacity: 0.8,
                      pointerEvents: 'all',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 128, 255, 0.15)'
                      }
                    },
                    '&.read-only-input .MuiInputBase-input': {
                      WebkitTextFillColor: '#66b3ff !important'
                    },
                    '&.read-only-input fieldset': {
                      borderColor: 'rgba(0, 128, 255, 0.3) !important'
                    },
                    '& .MuiInputBase-root': {
                      backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      color: '#fff',
                      opacity: 0.8,
                      pointerEvents: 'all',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    },
                    '& .MuiInputBase-input': {
                      cursor: 'text',
                      userSelect: 'text',
                      WebkitTextFillColor: '#fff !important',
                      '&::selection': {
                        backgroundColor: 'rgba(255, 255, 255, 0.2)'
                      }
                    },
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.15) !important'
                    }
                  }}
                  name={path}
                />
              </MDBox>
            </MDBox>
          );
        }
      }

      return [...priorityElements, ...disabledElements, ...regularElements];
    };

    return recursiveRender(formData);
  }, [
    formData,
    change,
    newProperties,
    handleAddProperty,
    handleCloseButton,
    handleExpandCollapseAll,
    handleNewPropertyChange
  ]);

  return <>{renderInputs}</>;
}

const checkPathInList = (path, list) => {
  // This is done because when we have an array of objects, the path is like this: 'principalsIds.0.firstName'
  // and we need a path to be like this: 'principalsIds.firstName'
  const cleanedPath = path
    .split('.')
    .filter((part) => isNaN(Number(part)))
    .join('.');

  // If the exact path exists in readOnlyData
  if (list?.includes(cleanedPath)) {
    return true;
  }

  // Check for nested paths and parent paths
  if (
    list?.some((listPath) => {
      // Case 1: Check if current path starts with any list path plus a dot
      // This handles cases where listPath is a parent path (e.g., 'sumsub' matches 'sumsub.sumsubId')
      if (cleanedPath.startsWith(listPath + '.')) {
        return true;
      }

      // Case 2: Check if list path starts with current path plus a dot
      // This handles cases where current path is a parent of a list path
      if (listPath.startsWith(cleanedPath + '.')) {
        return true;
      }

      // Case 3: Exact match (already handled above, but included for completeness)
      return cleanedPath === listPath;
    })
  ) {
    return true;
  }

  return false;
};

export default React.memo(RenderObjectInInputs);
