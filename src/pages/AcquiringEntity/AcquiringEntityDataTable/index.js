let tableData = {
  columns: [
    {
      header: 'Row Number',
      accessorKey: 'rowIndex',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Created at',
      accessorKey: 'createdAt',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Name',
      accessorKey: 'name',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Register Number',
      accessorKey: 'registrationNumber',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'MasterCard Match',
      accessorKey: 'mastercardMatch',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Visa Match',
      accessorKey: 'visaMatch',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Sumsub Answer',
      accessorKey: 'sumsubAnswer',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Sumsub Status',
      accessorKey: 'sumsubStatus',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'MID Config',
      accessorKey: 'midConfig',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'OmniPay Status',
      accessorKey: 'omnipayStatus',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'ACI Status',
      accessorKey: 'aciStatus',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Export',
      accessorKey: 'export',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Action',
      accessorKey: 'action',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    }
  ],
  rows: []
};

export default tableData;
