// Initial form structure for Acquiring Entity
export const initialAcquiringEntityFormData = {
  name: '',
  registrationNumber: '',
  countryOfRegistration: '',
  email: '',
  pspIsoReferralAgentName: '',
  taxId: '',
  correspondenceEmail: '',
  phone: '',
  country: '',
  city: '',
  state: '',
  zip: '',
  address1: '',
  address2: '',
  tradeOverInternet: null,
  websites: [
    {
      url: '',
      statementDescriptor: '',
      cityField: ''
    }
  ],
  principalRawData: [
    {
      firstName: '',
      middleName: '',
      lastName: '',
      email: '',
      phone: '',
      country: '',
      state: '',
      city: '',
      zip: '',
      address1: '',
      address2: '',
      dateOfBirth: '',
      passportNumber: '',
      driverLicenseNumber: '',
      positionInCompany: ''
    }
  ],
  businessCategoryVmss: '',
  entityType: 'Merchant',
  incorporationDate: '',
  submissionId: '',
  tradingName: '',
  address1TradingAs: '',
  address2TradingAs: '',
  cityTradingAs: '',
  stateTradingAs: '',
  zipTradingAs: '',
  countryTradingAs: '',
  additionalData: {
    currencies: [''],
    settlementCurrencies: [''],
    integrationsRequirements: {
      integrationType: '',
      pciComplianceStatus: '',
      thirdPartyGateway: '',
      integrationOption: '',
      paymentPageHostingPreference: '',
      paymentPageURL: '',
      mandatory3ds: null
    },
    businessOffering: {
      industryBusinessCategory: [
        {
          category: '',
          subcategory: ['']
        }
      ],
      productOrServicesDescription: ''
    },
    cryptoSettlement: [
      {
        currency: '',
        percentage: '',
        walletAddress: '',
        preferredNetwork: '',
        cryptocurrency: ''
      }
    ],
    targetMarketsAndDistribution: [
      {
        country: '',
        volumePercentage: ''
      }
    ],
    bankSettlement: [
      {
        currency: '',
        bankCountry: '',
        bankName: '',
        bankIban: '',
        bankCode: '',
        bankAccountHolder: ''
      }
    ],
    salesMetrics: {
      totalMonthlySalesVolume: '',
      expectedMonthlyVolume: '',
      numberOfExpectedMonthlyTransactions: '',
      averageTransactionAmount: '',
      highestTransactionAmount: '',
      lowestTransactionAmount: ''
    },
    shippingInfo: {
      shippingMethods: '',
      trackingNumber: '',
      shippingFeesAndDeliveryTimes: ''
    },
    rdrSettings: {
      rdrEnrollmentPreference: '',
      automaticRdrRefunds: {
        option: '',
        thresholdAmount: 0
      }
    },
    masterCardSettings: {
      masterCardEnrollmentPreference: '',
      automaticMasterCardRefunds: {
        option: '',
        thresholdAmount: 0
      }
    },
    settlementAccountMetrics: {
      monthlySalesVolume: '',
      estimatedMonthlyTransfers: '',
      billingMethods: [''],
      processedCreditCardsBefore: '',
      fraudAndChargebacksRates: [['']],
      isRevenueFromBusinessActivity: '',
      freeTrialZeroAmount: ''
    },
    contacts: {
      primary: {
        name: '',
        email: '',
        phone: '',
        other: ''
      }
    },
    // Industry-specific objects
    cryptoIndustry: [
      {
        cryptoActivityType: [''],
        cryptocurrenciesList: [''],
        nativeTokensSold: null,
        nativeTokensTypes: [''],
        customerBase: [''],
        conversions: [''],
        KYCStages: [''],
        tools: null,
        withdrawals: null,
        withdrawalsWalletTypes: [''],
        withdrawalsProcess: '',
        risks: null,
        yearsEngaging: '',
        training: null,
        pastTransactions: '',
        professionalBackground: ''
      }
    ],
    MSBIndustry: [
      {
        employeesNumber: '',
        annualTurnover: '',
        serviceTypes: [''],
        customerBase: [''],
        internationalTransactionsRatio: '',
        enhancedDueDiligence: null,
        KYCStages: [''],
        customerIDVerification: [''],
        preventionSystem: null,
        complianceEmployeesNumber: '',
        highRiskClientsRatio: '',
        complianceAudits: '',
        cryptoInvolvement: null,
        cryptoExposureDetails: ''
      }
    ],
    adultServices: [
      {
        contentSources: [''],
        contentConsentAgreements: null,
        contentVerificationMethods: '',
        contentPoviderAgeAndIDVerification: '',
        contentReviewProcess: '',
        liveStreamingControl: null,
        liveStreamingMonitoringAndRemoval: '',
        removalAppealProcess: '',
        marketingAndSearchTerms: '',
        potentiallyIllegalContentPreventionProcess: '',
        potentiallyIllegalContentComplaintProcess: null,
        illegalContentReporting: null,
        traffickingAndAbusePolicy: '',
        consentAgreementsRetention: null,
        offerAIContent: null,
        AIContentSources: '',
        AIContentAgreements: '',
        confirmNoAIDeepfakes: null
      }
    ],
    datingEscortData: [
      {
        antiTraffickingProgram: '',
        contentReviewProcess: '',
        contentRemovalAppeals: '',
        marketingAndSearchTermsCompliance: '',
        contentProviderConsentsDepiction: '',
        consentPublicDistribution: '',
        consentContentDownload: '',
        consentsValidationAndStorage: '',
        evidenceValidationAndRetention: '',
        marketingStrategy: '',
        fakeBotPrevention: ''
      }
    ],
    industrySpecificStatement: [
      {
        highValueKYC: null,
        insurance: null,
        highValuePlayerKYC: null,
        payoutReserves: null,
        bonusAbusePrevention: null,
        selfExclusionOrResponsibleTools: null,
        coolingOff: null,
        highValueGoods: null,
        gamblingInclusion: null,
        childProtection: null,
        ageVerification: null,
        guestOrAnnonymousAccounts: null,
        spendingLimits: null,
        tieredAccountSystem: null,
        kycStages: '',
        forexLicenseHolder: '',
        gamblingLicense: '',
        geofencing: '',
        fraudPrevention: '',
        minimumAgeRequirements: '',
        payoutWithdrawals: '',
        cardRevenueRatio: '',
        suspiciousPatternsPrevention: '',
        massBettingPrevention: '',
        singleOrMultiVendor: '',
        transacitonProcessing: '',
        disputeResolution: '',
        vendorsNumber: '',
        suspiciousAccountHandling: '',
        payoutReserveManagement: '',
        selfExclusionOrResponsibleToolsDescription: '',
        consumerProtectionMethods: [''],
        marketplaceTypes: [''],
        vendorOnboarding: [''],
        gamingServiceTypes: [''],
        currencyTypes: [''],
        transactionTypes: [''],
        accountCreationInformation: [''],
        fraudPreventionGmg: [''],
        digWalTypes: [''],
        serviceTypesDigWal: [''],
        targetAudience: [''],
        onboardingInformation: [''],
        IDVerification: ['']
      }
    ]
  }
};

// Helper function to get a fresh copy of the initial form data
export const getInitialFormData = () => {
  return JSON.parse(JSON.stringify(initialAcquiringEntityFormData));
};
