// Initial form structure for Acquiring Entity
export const initialAcquiringEntityFormData = {
  name: '',
  registrationNumber: '',
  countryOfRegistration: '',
  email: '',
  pspIsoReferralAgentName: '',
  taxId: '',
  correspondenceEmail: '',
  phone: '',
  country: '',
  city: '',
  state: '',
  zip: '',
  address1: '',
  address2: '',
  tradeOverInternet: false,
  websites: [
    {
      url: '',
      statementDescriptor: '',
      cityField: ''
    }
  ],
  principalRawData: [
    {
      firstName: '',
      middleName: '',
      lastName: '',
      email: '',
      phone: '',
      country: '',
      state: '',
      city: '',
      zip: '',
      address1: '',
      address2: '',
      dateOfBirth: '',
      passportNumber: '',
      driverLicenseNumber: '',
      positionInCompany: ''
    }
  ],
  businessCategoryVmss: '',
  entityType: 'Merchant',
  incorporationDate: '',
  submissionId: '',
  tradingName: '',
  address1TradingAs: '',
  address2TradingAs: '',
  cityTradingAs: '',
  stateTradingAs: '',
  zipTradingAs: '',
  countryTradingAs: '',
  additionalData: {
    currencies: [''],
    settlementCurrencies: [''],
    integrationsRequirements: {
      integrationType: '',
      pciComplianceStatus: '',
      thirdPartyGateway: '',
      integrationOption: '',
      paymentPageHostingPreference: '',
      paymentPageURL: '',
      mandatory3ds: false
    },
    businessOffering: {
      industryBusinessCategory: [
        {
          category: '',
          subcategory: ['']
        }
      ],
      productOrServicesDescription: ''
    },
    cryptoSettlement: [
      {
        currency: '',
        percentage: '',
        walletAddress: '',
        preferredNetwork: '',
        cryptocurrency: ''
      }
    ],
    targetMarketsAndDistribution: [
      {
        country: '',
        volumePercentage: ''
      }
    ],
    bankSettlement: [
      {
        currency: '',
        bankCountry: '',
        bankName: '',
        bankIban: '',
        bankCode: '',
        bankAccountHolder: ''
      }
    ],
    salesMetrics: {
      totalMonthlySalesVolume: '',
      expectedMonthlyVolume: '',
      numberOfExpectedMonthlyTransactions: '',
      averageTransactionAmount: '',
      highestTransactionAmount: '',
      lowestTransactionAmount: ''
    },
    shippingInfo: {
      shippingMethods: '',
      trackingNumber: '',
      shippingFeesAndDeliveryTimes: ''
    },
    rdrSettings: {
      rdrEnrollmentPreference: '',
      automaticRdrRefunds: {
        option: '',
        thresholdAmount: 0
      }
    },
    masterCardSettings: {
      masterCardEnrollmentPreference: '',
      automaticMasterCardRefunds: {
        option: '',
        thresholdAmount: 0
      }
    },
    settlementAccountMetrics: {
      monthlySalesVolume: '',
      estimatedMonthlyTransfers: '',
      billingMethods: [''],
      processedCreditCardsBefore: '',
      fraudAndChargebacksRates: [['']],
      isRevenueFromBusinessActivity: '',
      freeTrialZeroAmount: ''
    },
    contacts: {
      primary: {
        name: '',
        email: '',
        phone: '',
        other: ''
      }
    },
    // Industry-specific objects
    cryptoIndustry: [
      {
        cryptoActivityType: [''],
        cryptocurrenciesList: [''],
        nativeTokensSold: false,
        nativeTokensTypes: [''],
        customerBase: [''],
        conversions: [''],
        KYCStages: [''],
        tools: false,
        withdrawals: false,
        withdrawalsWalletTypes: [''],
        withdrawalsProcess: '',
        risks: false,
        yearsEngaging: '',
        training: false,
        pastTransactions: '',
        professionalBackground: ''
      }
    ],
    MSBIndustry: [
      {
        employeesNumber: '',
        annualTurnover: '',
        serviceTypes: [''],
        customerBase: [''],
        internationalTransactionsRatio: '',
        enhancedDueDiligence: false,
        KYCStages: [''],
        customerIDVerification: [''],
        preventionSystem: false,
        complianceEmployeesNumber: '',
        highRiskClientsRatio: '',
        complianceAudits: '',
        cryptoInvolvement: false,
        cryptoExposureDetails: ''
      }
    ],
    adultServices: [
      {
        contentSources: [''],
        contentConsentAgreements: false,
        contentVerificationMethods: '',
        contentPoviderAgeAndIDVerification: '',
        contentReviewProcess: '',
        liveStreamingControl: false,
        liveStreamingMonitoringAndRemoval: '',
        removalAppealProcess: '',
        marketingAndSearchTerms: '',
        potentiallyIllegalContentPreventionProcess: '',
        potentiallyIllegalContentComplaintProcess: false,
        illegalContentReporting: false,
        traffickingAndAbusePolicy: '',
        consentAgreementsRetention: false,
        offerAIContent: false,
        AIContentSources: '',
        AIContentAgreements: '',
        confirmNoAIDeepfakes: false
      }
    ],
    datingEscortData: [
      {
        antiTraffickingProgram: '',
        contentReviewProcess: '',
        contentRemovalAppeals: '',
        marketingAndSearchTermsCompliance: '',
        contentProviderConsentsDepiction: '',
        consentPublicDistribution: '',
        consentContentDownload: '',
        consentsValidationAndStorage: '',
        evidenceValidationAndRetention: '',
        marketingStrategy: '',
        fakeBotPrevention: ''
      }
    ],
    industrySpecificStatement: [
      {
        highValueKYC: false,
        insurance: false,
        highValuePlayerKYC: false,
        payoutReserves: false,
        bonusAbusePrevention: false,
        selfExclusionOrResponsibleTools: false,
        coolingOff: false,
        highValueGoods: false,
        gamblingInclusion: false,
        childProtection: false,
        ageVerification: false,
        guestOrAnnonymousAccounts: false,
        spendingLimits: false,
        tieredAccountSystem: false,
        kycStages: '',
        forexLicenseHolder: '',
        gamblingLicense: '',
        geofencing: '',
        fraudPrevention: '',
        minimumAgeRequirements: '',
        payoutWithdrawals: '',
        cardRevenueRatio: '',
        suspiciousPatternsPrevention: '',
        massBettingPrevention: '',
        singleOrMultiVendor: '',
        transacitonProcessing: '',
        disputeResolution: '',
        vendorsNumber: '',
        suspiciousAccountHandling: '',
        payoutReserveManagement: '',
        selfExclusionOrResponsibleToolsDescription: '',
        consumerProtectionMethods: [''],
        marketplaceTypes: [''],
        vendorOnboarding: [''],
        gamingServiceTypes: [''],
        currencyTypes: [''],
        transactionTypes: [''],
        accountCreationInformation: [''],
        fraudPreventionGmg: [''],
        digWalTypes: [''],
        serviceTypesDigWal: [''],
        targetAudience: [''],
        onboardingInformation: [''],
        IDVerification: ['']
      }
    ]
  }
};

// Helper function to get a fresh copy of the initial form data
export const getInitialFormData = () => {
  return JSON.parse(JSON.stringify(initialAcquiringEntityFormData));
};
