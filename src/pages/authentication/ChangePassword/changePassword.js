import { useState, useRef, useEffect } from 'react';
import { IconButton, InputAdornment, CircularProgress } from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import { getUsers } from 'requests/user';
import { changePassword } from 'helpers/changePassword';

function ChangePassword() {
  const toastId = useRef(null);
  const [formData, setFormData] = useState({
    oldPassword: '',
    password: '',
    confirmPassword: ''
  });
  const [currentUserId, setCurrentUserId] = useState();
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const handleClickShowOldPassword = () => setShowOldPassword(!showOldPassword);
  const handleClickShowPassword = () => setShowPassword(!showPassword);
  const handleClickShowConfirmPassword = () => setShowConfirmPassword(!showConfirmPassword);

  useEffect(() => {
    const fetchUser = async () => {
      const response = await getUsers(1, 0, { username: localStorage.getItem('userName') });
      if (response) {
        setCurrentUserId(response.data.docs[0]._id);
      }
    };

    fetchUser();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value
    }));
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await changePassword(e, formData, toastId, currentUserId, setFormData);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDTypography variant='button' fontWeight='medium' color='text'>
        Update Your Password
      </MDTypography>
      <MDBox
        bgColor='#323a54'
        borderRadius='lg'
        style={{
          padding: '20px'
        }}
      >
        <form onSubmit={onSubmit}>
          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type={showOldPassword ? 'text' : 'password'}
              label='Current Password'
              name='oldPassword'
              value={formData.oldPassword}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    <IconButton aria-label='toggle password visibility' onClick={handleClickShowOldPassword}>
                      {showOldPassword ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </MDBox>

          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type={showPassword ? 'text' : 'password'}
              label='New Password'
              name='password'
              value={formData.password}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    <IconButton aria-label='toggle password visibility' onClick={handleClickShowPassword}>
                      {showPassword ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </MDBox>

          <MDBox mb={2}>
            <MDInput
              className='remove-arrows-from-input'
              type={showConfirmPassword ? 'text' : 'password'}
              label='Confirm Password'
              name='confirmPassword'
              value={formData.confirmPassword}
              sx={{ width: { xs: '100%', lg: '40%', minWidth: '200px' } }}
              onChange={handleInputChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    <IconButton aria-label='toggle password visibility' onClick={handleClickShowConfirmPassword}>
                      {showConfirmPassword ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </MDBox>
          <MDBox display='flex' justifyContent='center'>
            <MDButton
              variant='gradient'
              color='info'
              type='submit'
              disabled={isLoading || !currentUserId}
              startIcon={isLoading ? <CircularProgress size={20} color='inherit' /> : null}
            >
              {isLoading ? 'Updating...' : 'Update'}
            </MDButton>
          </MDBox>
        </form>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default ChangePassword;
