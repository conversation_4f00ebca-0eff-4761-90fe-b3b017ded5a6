import MDBox from 'components/MDBox';
import IllustrationLayout from '../components';
import bgImage from 'assets/images/illustrations/illustration-reset.jpg';
import TwoFactorAuth from 'components/TwoFactorAuth';

function TwoFactorVerify() {
  return (
    <IllustrationLayout
      title='Two-Factor Authentication'
      description='Enter the 6-digit code from your authenticator app'
      illustration={bgImage}
    >
      <MDBox mt={2}>
        <TwoFactorAuth mode='verify' buttonText='Verify' />
      </MDBox>
    </IllustrationLayout>
  );
}

export default TwoFactorVerify;
