import { useNavigate } from 'react-router-dom';

import MDBox from 'components/MDBox';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';

// Authentication layout components
import IllustrationLayout from '../components';

// Image
import bgImage from 'assets/images/illustrations/illustration-reset.jpg';
import { signIn } from 'requests/transaction';
import { useState } from 'react';
import { Alert } from '@mui/material';

function Illustration() {
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const onSignIn = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const res = await signIn(username, password);
      const twoFAToken = res.data.twoFAToken;
      // Check if 2FA is already set up
      if (res.data.twoFactorEnabled) {
        // Navigate to 2FA verification page with username
        navigate('/authentication/2fa/verify', {
          state: { twoFAToken }
        });
      } else {
        // Navigate to 2FA setup page
        navigate('/authentication/2fa/setup', {
          state: { twoFAToken }
        });
      }
    } catch (error) {
      setError(error.response?.data?.message ?? error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <IllustrationLayout title='Sign In' description='Enter your email and password to sign in' illustration={bgImage}>
      <MDBox component='form' role='form' autoComplete='off' onSubmit={onSignIn}>
        <MDBox mb={2}>
          <MDInput
            type='text'
            label='Username'
            onChange={(e) => setUsername(e.target.value)}
            fullWidth
            autoComplete='username' // Add this line
          />
        </MDBox>
        <MDBox mb={2}>
          <MDInput
            type='password'
            label='Password'
            onChange={(e) => setPassword(e.target.value)}
            fullWidth
            autoComplete='current-password' // Add this line
          />
        </MDBox>
        {error && (
          <Alert sx={{ color: '#fa6161', fontSize: 14 }} severity='error' variant='outlined'>
            {error}
          </Alert>
        )}
        <MDBox mt={4} mb={1}>
          <MDButton variant='gradient' color='info' size='large' fullWidth type='submit' disabled={isLoading}>
            {isLoading ? 'Signing in...' : 'Sign in'}
          </MDButton>
        </MDBox>
      </MDBox>
    </IllustrationLayout>
  );
}

export default Illustration;
