import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import MDBox from 'components/MDBox';
import IllustrationLayout from '../components';
import bgImage from 'assets/images/illustrations/illustration-reset.jpg';
import TwoFactorAuth from 'components/TwoFactorAuth';
import { setup2FA } from 'requests/user';

function TwoFactorSetup() {
  const location = useLocation();
  const { twoFAToken } = location.state || {};
  const [qrCodeUrl, setQrCodeUrl] = useState(null);
  const [encrypted2FASecret, setEncrypted2FASecret] = useState(null);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchQRCode = async () => {
      if (!twoFAToken) {
        setError('Temporary token is missing. Please try logging in again.');
        return;
      }

      try {
        const response = await setup2FA(twoFAToken);
        setQrCodeUrl(response.data.qrCode);
        setEncrypted2FASecret(response.data.encrypted2FASecret);
      } catch (err) {
        setError(err.message || 'Failed to set up 2FA. Please try again.');
      }
    };

    fetchQRCode();
  }, [twoFAToken]);

  return (
    <IllustrationLayout
      title='Two-Factor Authentication Setup'
      description='Download "Google Authenticator" from App Store or Google Play Store and scan the QR code through the app'
      illustration={bgImage}
    >
      <MDBox mt={2}>
        <TwoFactorAuth
          mode='setup'
          qrCodeUrl={qrCodeUrl}
          setupError={error}
          buttonText='Activate'
          encrypted2FASecret={encrypted2FASecret}
        />
      </MDBox>
    </IllustrationLayout>
  );
}

export default TwoFactorSetup;
