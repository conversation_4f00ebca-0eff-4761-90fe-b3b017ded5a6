import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { Icon, IconButton, Tooltip } from '@mui/material';

import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import DataTable from 'examples/Tables/DataTable/DataTable';
import Footer from 'examples/Footer';

import MDTypography from 'components/MDTypography';
import MDBox from 'components/MDBox';

import tableData from './TransactionDataTable';

import { getPaxumTransactions } from 'requests/paxum';
import PaxumTransactionFilter from 'components/Filters/PaxumTransactionFilter/PaxumTransactionFilter';
import dayjs from 'dayjs';

function PaxumTransactions() {
  const [tableInfo, setTableInfo] = useState({ ...tableData });

  const [refreshData, setRefreshData] = useState(false);
  const [sorting, setSorting] = useState([]);
  const [submitHandler, setSubmitHandler] = useState(false);

  const [dialogOpen, setDialogOpen] = useState(false);
  const [request, setRequest] = useState(null);

  const today = dayjs();
  const sevenDaysAgo = today.subtract(30, 'day');
  const [startDate, setStartDate] = useState(sevenDaysAgo);
  const [endDate, setEndDate] = useState(today);
  const [filters, setFilters] = useState({ startDate, endDate });

  const transformData = (data) => {
    if (data.length > 0) {
      const rows = data.map((el) => {
        const resolved = el.isProceed ? 'True' : 'False';
        return {
          ...el,
          isProceed: resolved,
          action: <MDBox display='flex'>{editButton(el.uniqueId)}</MDBox>
        };
      });
      return rows;
    } else {
      return [];
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <DataTable
        table={tableInfo}
        canSearch
        transformData={transformData}
        fetchingFunction={getPaxumTransactions}
        submitHandler={submitHandler}
        filters={filters}
        setFilters={setFilters}
        refreshData={refreshData}
        sorting={sorting}
        setSorting={setSorting}
        filtersComponent={
          <PaxumTransactionFilter
            filters={filters}
            setFilters={setFilters}
            setSubmitHandler={setSubmitHandler}
            startDate={startDate}
            setStartDate={setStartDate}
            endDate={endDate}
            setEndDate={setEndDate}
            sorting={sorting}
          />
        }
      />
      <Footer />
    </DashboardLayout>
  );
}

function editButton(uniqueId) {
  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
      <Tooltip title='View'>
        <NavLink to={`/dashboards/visa-direct/paxum-transactions/view/${uniqueId}`}>
          <MDTypography fontSize='0.875rem'>
            <IconButton size='small' color='info'>
              <Icon fontSize='small'>visibility</Icon>
            </IconButton>
          </MDTypography>
        </NavLink>
      </Tooltip>
    </MDBox>
  );
}

export default PaxumTransactions;
