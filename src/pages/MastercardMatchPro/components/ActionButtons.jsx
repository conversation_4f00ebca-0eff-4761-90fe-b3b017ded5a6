import { Tooltip } from '@mui/material';
import DialogPopup from 'components/DialogPopup/DialogPopup';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { changeMastercardMatchStatusManuallyByRefNumber } from 'requests/acquiringService';

export default function ActionButtons({ acquiringEntity, mastercardRefNumberMatched }) {
  const [openDialog, setOpenDialog] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [typeOfAction, setTypeOfAction] = useState('');
  const navigate = useNavigate();

  async function handleClick() {
    if (!acquiringEntity._id || !typeOfAction) {
      toast.error('Something went wrong!', {
        className: 'toast-width-600',
        autoClose: true,
        closeOnClick: false
      });
      return;
    }

    try {
      await changeMastercardMatchStatusManuallyByRefNumber(
        acquiringEntity._id,
        typeOfAction,
        mastercardRefNumberMatched
      );

      toast.success('Action performed successfully!');
      navigate(`/dashboards/mastercard-match-pro-references/${acquiringEntity._id}`);
    } catch (error) {
      let message = '';

      if (error.message.includes('\n')) {
        message = error.message.split('\n');
      } else {
        message = error.message;
      }

      // If error.message is an array, join it with line breaks
      if (Array.isArray(message)) {
        message = (
          <>
            {message.map((item, index) => (
              <p key={index}>{item}</p>
            ))}
          </>
        );
      }

      toast.error(message, {
        className: 'toast-width-600',
        autoClose: false,
        closeOnClick: false
      });
    }
  }
  function resetState() {
    setTypeOfAction('');
    setTitle('');
    setDescription('');
    setOpenDialog(false);
  }

  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'flex-start', marginLeft: '10px', marginBottom: '10px' }}>
      <Tooltip title='Approve transaction'>
        <MDTypography fontSize='0.875rem'>
          <MDButton
            variant='gradient'
            color='success'
            sx={{
              marginRight: '10px'
            }}
            onClick={() => {
              setTypeOfAction('approve');
              setOpenDialog(true);
              setTitle('Acquiring Approval');
              setDescription('Are you sure you want to approve this Acquiring?');
            }}
          >
            Approve
          </MDButton>
        </MDTypography>
      </Tooltip>
      <Tooltip title='Approve transaction'>
        <MDTypography fontSize='0.875rem'>
          <MDButton
            variant='gradient'
            color='error'
            onClick={() => {
              setTypeOfAction('reject');
              setOpenDialog(true);
              setTitle('Acquiring Rejection');
              setDescription('Are you sure you want to reject this Acquiring?');
            }}
          >
            Reject
          </MDButton>
        </MDTypography>
      </Tooltip>

      <DialogPopup
        open={openDialog}
        title={title}
        description={description}
        buttonsActions={
          <>
            <MDButton
              variant='text'
              color='error'
              onClick={() => {
                resetState();
              }}
            >
              No
            </MDButton>
            <MDButton
              variant='text'
              color='success'
              onClick={async () => {
                await handleClick();
                resetState();
              }}
            >
              Yes
            </MDButton>
          </>
        }
      />
    </MDBox>
  );
}
