import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

import tableData from './MastercardMatchProDataTable';
import { useCallback, useEffect, useMemo, useState } from 'react';
import Footer from 'examples/Footer';

import MDBox from 'components/MDBox';
import { IconButton, Badge } from '@mui/material';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { getMastercardMatchProMatches } from 'requests/acquiringService';
import { toast } from 'react-toastify';
import ExpandingDataTable from 'examples/Tables/DataTable/ExpandingDataTable';
import ActionButtons from './components/ActionButtons';
import { JsonRenderer } from './components/ColumnExpansion';
import MastercardMatchProTableHeader from '../MastercardMatchProReferencces/components/TableHeader';

function MastercardMatchPro() {
  const [tableInfo, setTableInfo] = useState(tableData);
  const [filters, setFilters] = useState({});
  const [submitHandler, setSubmitHandler] = useState(false);
  const [sorting, setSorting] = useState([]);
  const { acquiringEntityId } = useParams();
  const [searchParams] = useSearchParams();
  const [acquiringEntity, setAcquiringEntity] = useState(null);
  const [lastMatch, setLastMatch] = useState(null);
  const mastercardRefNumberMatched = searchParams.get('mastercardRefNumberMatched');
  const navigate = useNavigate();

  // Add cleanup effect
  useEffect(() => {
    return () => {
      // This will run when component unmounts
      toast.dismiss();
    };
  }, []);

  let dynamicColumnsData;
  const dataTableData = useCallback(async (limit, page, filters, sorting) => {
    const loadingToast = toast.loading('Fetching information...');

    try {
      const result = await getMastercardMatchProMatches(
        limit,
        page,
        filters,
        sorting,
        acquiringEntityId,
        mastercardRefNumberMatched
      );
      if (result?.data?.docs?.[0]) {
        setAcquiringEntity(result?.data?.docs?.[0]);
        setLastMatch(result?.data?.lastMatch);
      }
      dynamicColumnsData = ensureUrlsBeforeUrlGroups(result?.data?.columnsData);

      toast.dismiss(loadingToast);

      return result;
    } catch (error) {
      toast.dismiss(loadingToast);
      let message = '';

      if (error.message.includes('\n')) {
        message = error.message.split('\n');
      } else {
        message = error.message;
      }

      // If error.message is an array, show each message in a new line
      if (Array.isArray(message)) {
        message = (
          <>
            {message.map((item, index) => (
              <p key={index}>{item}</p>
            ))}
          </>
        );
      }

      toast.error(message, {
        className: 'toast-width-600',
        autoClose: false,
        closeOnClick: false
      });
    }
  }, []);

  const dynamicColumns = useCallback(() => {
    const doNotShowColumns = ['_id'];
    let columns;
    if (dynamicColumnsData && dynamicColumnsData.length > 0) {
      columns = dynamicColumnsData.map((el, i) => {
        if (doNotShowColumns.includes(el)) {
          return null;
        }

        return {
          header: el,
          accessorKey: el,
          cell: (info) => {
            let principalNumber = null;
            const match = el.match(/^principals(\d+)$/);
            if (match) {
              principalNumber = match[1];
            }
            const columnValue = info.getValue();
            if (typeof columnValue === 'object' && columnValue !== null) {
              const hasM01orM02 = containsValues(info.row.original?.merchantMatch?.principalMatches);
              return (
                <>
                  {principalNumber && hasM01orM02 ? (
                    <Badge
                      color='error'
                      variant='dot'
                      sx={{
                        '& .MuiBadge-badge': {
                          right: 4,
                          top: 4
                        }
                      }}
                    >
                      {info.row.getCanExpand() ? (
                        <IconButton
                          aria-label='expand row'
                          size='small'
                          color='info'
                          onClick={info.row.getToggleExpandedHandler()}
                          style={{ cursor: 'pointer' }}
                        >
                          {info.row.getIsExpanded() ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}{' '}
                          {`${info.row.original?.[el]?.firstName ?? ''} ${info.row.original?.[el]?.lastName ?? ''}`}
                        </IconButton>
                      ) : (
                        ''
                      )}
                    </Badge>
                  ) : info.row.getCanExpand() ? (
                    <IconButton
                      aria-label='expand row'
                      size='small'
                      color='info'
                      onClick={info.row.getToggleExpandedHandler()}
                      style={{ cursor: 'pointer' }}
                    >
                      {info.row.getIsExpanded() ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}{' '}
                      {`${info.row.original?.[el]?.firstName ?? ''} ${info.row.original?.[el]?.lastName ?? ''}`}
                    </IconButton>
                  ) : (
                    ''
                  )}

                  {info.row.getIsExpanded() && (
                    <JsonRenderer
                      data={info.getValue()}
                      info={info}
                      principalNumber={principalNumber}
                      compareMatch={compareMatch}
                    />
                  )}
                </>
              );
            } else {
              let color = 'white';
              if (el.includes('address')) {
                color = compareMatch(info.row.original?.merchantMatch, 'address');
              } else {
                color = compareMatch(info.row.original?.merchantMatch, el);
              }

              return (
                <MDBox color={color} sx={{ display: 'flex', justifyContent: 'space-around' }}>
                  {' '}
                  {info.renderValue()}{' '}
                </MDBox>
              );
            }
          },
          footer: (props) => props.column.id
        };
      });

      columns = columns.filter((column) => column !== null);
      return columns;
    }

    return columns || tableData.columns;
  }, []);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <ExpandingDataTable
        table={tableInfo}
        canSearch
        fetchingFunction={dataTableData}
        submitHandler={submitHandler}
        filters={filters}
        sorting={sorting}
        setSorting={setSorting}
        dynamicColumns={dynamicColumns}
        // filtersComponent={
        //   <MastercardMatchProTableHeader
        //     acquiringEntityId={acquiringEntityId}
        //     setSubmitHandler={setSubmitHandler}
        //     lastMatch={lastMatch}
        //   />
        // }
        tableFooter={
          <ActionButtons acquiringEntity={acquiringEntity} mastercardRefNumberMatched={mastercardRefNumberMatched} />
        }
      />
      <Footer />
    </DashboardLayout>
  );
}

// CHeck if ir have some values of M01 or M02
function containsValues(obj) {
  const values = ['M01', 'M02'];
  if (obj === null) return false;
  for (let key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      if (Array.isArray(obj[key])) {
        for (let item of obj[key]) {
          if (containsValues(item, values)) return true;
        }
      } else {
        if (containsValues(obj[key], values)) return true;
      }
    } else if (values.includes(obj[key])) {
      return true;
    }
  }
  return false;
}

const getMatchColor = (value) => {
  switch (value) {
    case 'M00':
      return 'white';
    case 'M01':
      return '#FFD700'; // yellow
    case 'M02':
      return 'red';
    default:
      return 'white';
  }
};

function compareMatch(merchantMatch, key) {
  if (merchantMatch?.hasOwnProperty(key)) {
    return getMatchColor(merchantMatch[key]);
  }
  return 'white';
}

function ensureUrlsBeforeUrlGroups(columnsData) {
  let urlsIndex = columnsData.indexOf('urls');
  let urlGroupIndex = columnsData.findIndex((item) => item.startsWith('urlGroups[0]'));

  // If urlGroups[0] exists
  if (urlGroupIndex !== -1 && urlsIndex !== -1) {
    // Remove the element from its original position
    const element = columnsData.splice(urlsIndex, 1)[0];

    // Insert the element at the new position
    columnsData.splice(urlGroupIndex - 1, 0, element);
  }

  return columnsData;
}

export default MastercardMatchPro;
