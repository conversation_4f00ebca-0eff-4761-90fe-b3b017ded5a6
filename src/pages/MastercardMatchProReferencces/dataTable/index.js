let tableData = {
  columns: [
    {
      header: 'URL',
      accessor<PERSON>ey: 'url',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Resolved Match',
      accessorKey: 'resolvedMastercardMatch',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Match Count',
      accessor<PERSON>ey: 'mastercardTotalCountMatches',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'Date',
      accessorKey: 'createdAt',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    }
  ],
  rows: []
};

export default tableData;
