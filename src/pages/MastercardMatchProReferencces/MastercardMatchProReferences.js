import { useCallback, useState } from 'react';
import { toast } from 'react-toastify';
import { NavLink, useNavigate, useParams } from 'react-router-dom';
import { Icon, IconButton, Tooltip } from '@mui/material';

import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

import DataTable from 'examples/Tables/DataTable/DataTable';
import tableData from './dataTable';
import Footer from 'examples/Footer';

import { getMastercardMatchProReferences } from 'requests/acquiringService';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import { formatDate } from 'helpers/dateFormater';
import ExpandingDataTable from 'examples/Tables/DataTable/ExpandingDataTable';
import MastercardMatchProTableHeader from 'pages/MastercardMatchProReferencces/components/TableHeader';

import { changeMastercardMatchStatusManually } from 'requests/acquiringService';
import ActionButtons from 'components/matches/ActionButtons';

function MastercardMatchProReferences() {
  const [tableInfo, setTableInfo] = useState(tableData);
  const [filters, setFilters] = useState({});
  const [submitHandler, setSubmitHandler] = useState(false);
  const [sorting, setSorting] = useState([]);
  const { acquiringEntityId } = useParams();
  const navigate = useNavigate();
  const [matchDataId, setMatchDataId] = useState(null);
  const [failedMatchRequests, setFailedMatchRequests] = useState(null);
  const [lastMatch, setLastMatch] = useState(null);
  const [totalRequests, setTotalRequests] = useState(null);

  const transformData = useCallback(
    (data) => {
      const rows = data.map((el) => ({
        url: el.url,
        isMatch: el.isMatch?.toString().toUpperCase(),
        resolvedMastercardMatch: matchButton({ data: el, navigate, acquiringEntityId }),
        mastercardTotalCountMatches: el.mastercardTotalCountMatches,
        createdAt: el.createdAt ? formatDate(el.createdAt) : ''
      }));
      return rows;
    },
    [navigate, acquiringEntityId]
  );

  const dataTableData = useCallback(async (limit, page, filters, sorting) => {
    const loadingToast = toast.loading('Fetching information...');

    try {
      const result = await getMastercardMatchProReferences(limit, page, filters, sorting, acquiringEntityId);
      setMatchDataId(result.data._id);
      setFailedMatchRequests(result.data.failedRequests);
      setLastMatch(result.data.createdAt);
      setTotalRequests(result.data.totalRequests);
      toast.dismiss(loadingToast);

      return result;
    } catch (error) {
      toast.dismiss(loadingToast);
      let message = '';

      if (error.message.includes('\n')) {
        message = error.message.split('\n');
      } else {
        message = error.message;
      }

      // If error.message is an array, show each message in a new line
      if (Array.isArray(message)) {
        message = (
          <>
            {message.map((item, index) => (
              <p key={index}>{item}</p>
            ))}
          </>
        );
      }

      toast.error(message, {
        className: 'toast-width-600',
        autoClose: false,
        closeOnClick: false
      });
    }
  }, []);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <ExpandingDataTable
        entriesPerPage={{ defaultValue: 1000, entries: [1000] }}
        table={tableInfo}
        canSearch
        fetchingFunction={dataTableData}
        transformData={transformData}
        submitHandler={submitHandler}
        filters={filters}
        sorting={sorting}
        setSorting={setSorting}
        filtersComponent={
          <MastercardMatchProTableHeader
            acquiringEntityId={acquiringEntityId}
            setSubmitHandler={setSubmitHandler}
            lastMatch={lastMatch}
            failedMatchRequests={failedMatchRequests}
            totalRequests={totalRequests}
          />
        }
        tableFooter={
          <ActionButtons
            acquiringEntityId={acquiringEntityId}
            matchDataId={matchDataId}
            changeStatusFunction={changeMastercardMatchStatusManually}
            navigateTo={'/dashboards/acquiring-entity'}
          />
        }
        // tableFooter={<ActionButtons acquiringEntityId={acquiringEntityId} matchDataId={matchDataId} />}
      />
      <Footer />
    </DashboardLayout>
  );
}

function matchButton({ data, navigate, acquiringEntityId }) {
  const resolvedMatch = data?.resolvedMastercardMatch;
  const isMatch = data?.isMatch;

  let iconMatch = null;
  let buttonText = null;
  let color = '#53db53';
  let title = '';

  if (resolvedMatch === true) {
    buttonText = 'Resolved by Ryvyl';
    iconMatch = 'thumb_up';
  } else if (resolvedMatch === false) {
    buttonText = 'Rejected by Ryvyl';
    iconMatch = 'thumb_down';
    color = 'red';
  } else if (isMatch === true) {
    buttonText = 'Match';
    iconMatch = 'close';
    color = 'red';
  } else if (isMatch === false) {
    buttonText = 'No Match';
    iconMatch = 'check';
  } else if (resolvedMatch === null || resolvedMatch === undefined) {
    buttonText = 'Need to resolve';
    iconMatch = 'close';
    color = 'red';
  }
  return (
    <MDBox sx={{ display: 'flex', justifyContent: 'space-around' }}>
      <Tooltip title={title}>
        <MDTypography fontSize='0.875rem'>
          <NavLink
            to={`/dashboards/mastercard-match-pro/${acquiringEntityId}?mastercardRefNumberMatched=${data?.mastercardRefNumberMatched}`}
          >
            <IconButton size='small' color='info'>
              <MDBox display='flex' alignItems='center' gap={1}>
                <Icon sx={{ color: color }} fontSize='small'>
                  {iconMatch}
                </Icon>
                <MDTypography ml={1} sx={{ color: color }} fontSize='0.875rem'>
                  {buttonText}
                </MDTypography>
              </MDBox>
            </IconButton>
          </NavLink>
        </MDTypography>
      </Tooltip>
    </MDBox>
  );
}
export default MastercardMatchProReferences;
