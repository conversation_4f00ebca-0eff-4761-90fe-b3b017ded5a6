import { Icon, IconButton, Tooltip } from '@mui/material';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { getTransactionPDF } from 'requests/transaction';

let tableData = {
  columns: [
    {
      header: 'UNIQUE ID',
      accessorKey: 'UNIQUE_ID',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'REFERENCE ID',
      accessorKey: 'REFERENCE_ID',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'REFERENCE NUMBER',
      accessorKey: 'REFERENCE_NUMBER',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },
    {
      header: 'STATUS',
      accessorKey: 'TRANSACTION_STATUS',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },

    {
      header: 'CREATED AT',
      accessorKey: 'TIMESTAMP',
      cell: (info) => info.getValue(),
      footer: (props) => props.column.id
    },

    {
      header: 'Export',
      accessorKey: 'action',
      footer: (props) => props.column.id,
      cell: (info) => {
        const [disabledPdfButton, setDisabledPdfButton] = useState(false);
        const handleDownload = async () => {
          setDisabledPdfButton(true);

          try {
            await getTransactionPDF(info.row.original.UNIQUE_ID);

            setTimeout(() => {
              setDisabledPdfButton(false);
            }, 5000);
          } catch (error) {
            setDisabledPdfButton(false);
            toast.error(error.message, {
              className: 'toast-width-600'
            });
          }
        };

        return (
          <MDBox display='flex'>
            <MDBox
              sx={{
                display: 'flex',
                justifyContent: 'space-around'
              }}
            >
              <Tooltip title='Download PDF'>
                <MDTypography fontSize='0.875rem'>
                  {disabledPdfButton ? (
                    <IconButton size='small' color='inherit' disabled={true}>
                      <Icon fontSize='small'>picture_as_pdf</Icon>
                    </IconButton>
                  ) : (
                    <IconButton size='small' color='info' onClick={handleDownload}>
                      <Icon fontSize='small'>picture_as_pdf</Icon>
                    </IconButton>
                  )}
                  ;
                </MDTypography>
              </Tooltip>
            </MDBox>
          </MDBox>
        );
      }
    }
  ],
  rows: []
};

export default tableData;
