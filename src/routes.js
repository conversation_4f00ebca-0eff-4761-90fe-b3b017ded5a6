// Material Dashboard 2 PRO React pages
import SignIn from 'pages/authentication/sign-in';

// Material Dashboard 2 PRO React components
import MDAvatar from 'components/MDAvatar';

// @mui icons
import Icon from '@mui/material/Icon';
import VisaInvalidTransactions from 'pages/VisaInvalidTransactions/visaInvalidTransactions';
import EditTransaction from 'pages/VisaInvalidTransactions/EditTransaction/EditTransaction';
import SignOut from 'pages/authentication/signOut/signOut';
import SumsubTransactions from 'pages/SumsubTransactions/SumsubTransactions';
import VisaDirectTransactions from 'pages/VisaDirectTransactions/VisaDirectTransactions';
import VisaDirectAccountBalance from 'pages/VisaDirectAccountBalance/VisaDirectAccountBalance';
import CesopReporting from 'pages/cesop-reporting/CesopReporting';
import PaxumTransactions from 'pages/PaxumTransactions/PaxumTransactions';
import ViewTransaction from 'pages/PaxumTransactions/ViewTransaction/ViewTransaction';
import AllTransactions from 'pages/AllTransactions/AllTransactions';
import AmlBatchApplicant from 'pages/AmlBatchApplicant/AmlBatchApplicant';
import Merchants from 'pages/aci-onboarding/Merchants';
import AmlCustomers from 'pages/AmlCustomers/AmlCustomers';
import AcquiringEntity from 'pages/AcquiringEntity/AcquiringEntity';
import MastercardMatchPro from 'pages/MastercardMatchPro/MastercardMatchPro';
import VisaVmss from 'pages/VisaVmss/VisaVmss';
import EditAcquiringEntity from 'pages/AcquiringEntity/EditAcquiringEntity/EditAcquiringEntity';
import UserManagement from 'pages/UserManagement/UserManagement';
import ChangePassword from 'pages/authentication/ChangePassword/changePassword';
import EditUser from 'pages/UserManagement/EditUser/EditUser';
import CreateUser from 'pages/UserManagement/CreateUser/CreateUser';
import TwoFactorVerify from 'pages/authentication/TwoFactorVerify';
import TwoFactorSetup from 'pages/authentication/TwoFactorSetup';
import MastercardMatchProReferences from 'pages/MastercardMatchProReferencces/MastercardMatchProReferences';
import CreateEntity from 'pages/AcquiringEntity/CreateEntity/CreateEntity';
import AcquiringEntityFiles from 'pages/AcquiringEntity/AcquiringEntityFiles/AcquiringEntityFiles';
const routes = [
  /**
   * @params authentication - does the router need authentication {false/true}
   * @params action - does the router need authorization row of access action {manage/read/create/update/delete}
   * @params object - does the router need authorization row of access object {all/visa/visadirect}
   * @params notAuthorized - then the user do not have authorization access where to redirect him
   */
  {
    type: 'collapse',
    name: 'User', // This will be dynamically replaced in Sidenav component
    key: 'username',
    icon: <MDAvatar alt='User' size='sm' />,
    collapse: [
      {
        name: 'Sign Out',
        key: 'sign-out',
        route: '/authentication/sign-out',
        component: <SignOut />
      },
      {
        name: 'Change Password',
        key: 'change-password',
        route: '/change-password',
        component: <ChangePassword />
      }
    ]
  },
  { type: 'divider', key: 'divider-0' },
  {
    type: 'collapse',
    name: 'Dashboards',
    key: 'dashboards',
    authentication: true,
    icon: <Icon fontSize='medium'>dashboard</Icon>,
    collapse: [
      {
        name: 'Proof of Payment',
        key: 'proof-of-payment',
        route: '/dashboards/proof-of-payment',
        authentication: true,
        component: <AllTransactions />
      },

      {
        name: 'AML',
        key: 'aml',
        route: '/dashboards/aml',
        authentication: true,
        collapse: [
          {
            name: 'Transactions',
            key: 'transactions',
            route: '/dashboards/aml/transactions',
            authentication: true,
            component: <SumsubTransactions />
          },
          {
            name: 'Customers',
            key: 'customers',
            route: '/dashboards/aml/customers',
            authentication: true,
            component: <AmlCustomers />
          }
        ]
      },
      {
        name: 'Visa Direct',
        key: 'visa-direct',
        route: '/dashboards/visa-direct',
        authentication: true,
        collapse: [
          {
            name: 'Transactions',
            key: 'visa-transactions',
            route: '/dashboards/visa-direct/transactions',
            authentication: true,
            component: <VisaDirectTransactions />
          },
          {
            name: 'Account Balances',
            key: 'visa-balances',
            route: '/dashboards/visa-direct/account-balances',
            authentication: true,
            component: <VisaDirectAccountBalance />
          },
          {
            name: 'Stuck Transactions',
            key: 'stuck-transactions',
            route: '/dashboards/visa-direct/stuck-transactions',
            authentication: true,
            component: <VisaInvalidTransactions />,
            action: 'read',
            object: 'visa',
            notAuthorized: '/dashboards/aml/transactions'
          },
          {
            name: 'Paxum Transactions',
            key: 'paxum-transactions',
            route: '/dashboards/visa-direct/paxum-transactions',
            authentication: true,
            component: <PaxumTransactions />,
            action: 'read',
            object: 'visa',
            notAuthorized: '/dashboards/aml/transactions'
          }
        ]
      },
      {
        name: 'CESOP Reports',
        key: 'cesop-reports',
        icon: <Icon fontSize='medium'>content_paste</Icon>,
        route: '/dashboards/cesop-reports',
        authentication: true,
        component: <CesopReporting />
      },
      {
        name: 'AML Batch Applicant',
        key: 'aml-batch-applicant',
        icon: <Icon fontSize='medium'>content_paste</Icon>,
        route: '/dashboards/aml-batch-applicant',
        authentication: true,
        component: <AmlBatchApplicant />
      },
      {
        name: 'Acquiring Entity',
        key: 'acquiring-entity',
        route: '/dashboards/acquiring-entity',
        authentication: true,
        component: <AcquiringEntity />
      },

      {
        name: 'Merchants',
        key: 'merchant-onboarding',
        route: '/dashboards/merchants',
        authentication: true,
        collapse: [
          {
            name: 'Onboarding',
            key: 'onboarding',
            route: '/dashboards/merchants/onboarding',
            authentication: true,
            component: <Merchants />
          }
        ]
      },
      {
        name: 'User Management',
        key: 'user-management',
        route: '/dashboards/user-management',
        authentication: true,
        component: <UserManagement />
      }
    ]
  },
  {
    type: 'none',
    name: 'Authentication',
    key: 'authentication',
    icon: <Icon fontSize='medium'>content_paste</Icon>,
    route: '/authentication/sign-in',
    component: <SignIn />
  },
  {
    type: 'none',
    name: '2FA Verification',
    key: '2fa-verify',
    route: '/authentication/2fa/verify',
    component: <TwoFactorVerify />
  },
  {
    type: 'none',
    name: '2FA Setup',
    key: '2fa-setup',
    route: '/authentication/2fa/setup',
    component: <TwoFactorSetup />
  },
  {
    name: 'Edit Invalid Transaction',
    key: 'edit-invalid-transaction',
    route: '/dashboards/visa-direct/stuck-transactions/edit/:referenceId',
    authentication: true,
    component: <EditTransaction />
  },
  {
    name: 'View Transaction',
    key: 'view-transaction',
    route: '/dashboards/visa-direct/paxum-transactions/view/:uniqueId',
    authentication: true,
    component: <ViewTransaction />
  },
  {
    type: 'none',
    name: 'Mastercard Match Pro References',
    key: 'mastercard-match-pro-references',
    route: '/dashboards/mastercard-match-pro-references/:acquiringEntityId',
    authentication: true,
    component: <MastercardMatchProReferences />
  },
  {
    type: 'none',
    name: 'Mastercard Match Pro',
    key: 'mastercard-match-pro',
    route: '/dashboards/mastercard-match-pro/:acquiringEntityId',
    authentication: true,
    component: <MastercardMatchPro />
  },
  {
    type: 'none',
    name: 'Visa VMSS',
    key: 'visa-vmss',
    route: '/dashboards/visa-vmss/:acquiringEntityId',
    authentication: true,
    component: <VisaVmss />
  },
  {
    name: 'Edit Invalid Transaction',
    key: 'edit-invalid-transaction',
    route: '/dashboards/acquiring-entity/edit/:acquiringEntityId',
    authentication: true,
    component: <EditAcquiringEntity />
  },
  {
    name: 'Edit User',
    key: 'edit-user',
    route: '/dashboards/user-management/:userId',
    authentication: true,
    component: <EditUser />
  },
  {
    name: 'Create User',
    key: 'create-user',
    route: '/dashboards/user-management/create',
    authentication: true,
    component: <CreateUser />
  },
  {
    name: 'Create Entity',
    key: 'create-entity',
    route: '/dashboards/acquiring-entity/create',
    authentication: true,
    component: <CreateEntity />
  },
  {
    name: 'Acquiring Entity Files',
    key: 'acquiring-entity-files',
    route: '/dashboards/acquiring-entity/files/:entityId',
    authentication: true,
    component: <AcquiringEntityFiles />
  }
];

export default routes;
