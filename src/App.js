/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

import { useState, useEffect, useMemo } from 'react';

// react-router components
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';

// @mui material components
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 PRO React components
import MDBox from 'components/MDBox';

// Material Dashboard 2 PRO React examples
import Sidenav from 'examples/Sidenav';
import Configurator from 'examples/Configurator';

// Material Dashboard 2 PRO React Dark Mode themes
import themeDark from 'assets/theme-dark';

// Material Dashboard 2 PRO React routes
import routes from 'routes';

// Material Dashboard 2 PRO React contexts
import { useMaterialUIController, setMiniSidenav, setOpenConfigurator } from 'context';

// Images
import brandWhite from 'assets/images/logo-ct.png';
import 'react-toastify/dist/ReactToastify.css';
import ProtectedRoute from 'components/ProtectedRoutes/ProtectedRoutes';
import { useAuth } from 'context/auth';

import 'App.css';
import RouterAuthorization from 'components/ProtectedRoutes/RouterAuthorization';

export default function App() {
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, direction, layout, openConfigurator, sidenavColor } = controller;
  const [onMouseEnter, setOnMouseEnter] = useState(false);
  const { pathname } = useLocation();
  const { auth, setAuth } = useAuth();
  const memoizedRoutes = useMemo(() => getRoutes(routes), [auth.token, auth.loading]);

  // Open sidenav when mouse enter on mini sidenav
  const handleOnMouseEnter = () => {
    if (miniSidenav && !onMouseEnter) {
      setMiniSidenav(dispatch, false);
      setOnMouseEnter(true);
    }
  };

  // Close sidenav when mouse leave mini sidenav
  const handleOnMouseLeave = () => {
    if (onMouseEnter) {
      setMiniSidenav(dispatch, true);
      setOnMouseEnter(false);
    }
  };

  // Change the openConfigurator state
  const handleConfiguratorOpen = () => setOpenConfigurator(dispatch, !openConfigurator);

  // Setting the dir attribute for the body element
  useEffect(() => {
    document.body.setAttribute('dir', direction);
  }, [direction]);

  // Setting page scroll to 0 when changing the route
  useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
  }, [pathname]);

  function getRoutes(allRoutes) {
    return allRoutes.map((route, index) => {
      if (route.collapse) {
        return getRoutes(route.collapse);
      }

      if (route.route) {
        if (route.authentication) {
          return (
            <Route
              key={`route-no-auth-${route.key || index}`}
              element={
                <ProtectedRoute navigateAddress={'/authentication/sign-in'} authentication={route.authentication} />
              }
            >
              <Route
                element={
                  <RouterAuthorization
                    action={route.action}
                    object={route.object}
                    notAuthorized={route.notAuthorized}
                  />
                }
              >
                <Route exact path={route.route} element={route.component} key={route.key} />
              </Route>
            </Route>
          );
        } else {
          if (auth.loading) {
            return (
              <Route key={`route-no-auth-${route.key || index}`} path={route.route} element={<div>Loading...</div>} />
            );
          }
          if (auth.token && auth.loading == false && route.route == '/authentication/sign-in') {
            return (
              <Route
                key={`route-no-auth-${route.key || index}`}
                element={
                  <RouterAuthorization
                    action={route.action}
                    object={route.object}
                    notAuthorized={route.notAuthorized}
                  />
                }
              >
                <Route key={route.key} path={route.route} element={<Navigate to='/dashboards/aml/transactions' />} />
              </Route>
            );
          }
          return (
            <Route
              key={`route-no-auth-${route.key || index}`}
              element={
                <RouterAuthorization action={route.action} object={route.object} notAuthorized={route.notAuthorized} />
              }
            >
              <Route exact path={route.route} element={route.component} key={route.key} />
            </Route>
          );
        }
      }

      return null;
    });
  }

  const configsButton = (
    <MDBox
      display='flex'
      justifyContent='center'
      alignItems='center'
      width='3.25rem'
      height='3.25rem'
      bgColor='white'
      shadow='sm'
      borderRadius='50%'
      position='fixed'
      right='2rem'
      bottom='2rem'
      zIndex={99}
      color='dark'
      sx={{ cursor: 'pointer' }}
      onClick={handleConfiguratorOpen}
    >
      <Icon fontSize='small' color='inherit'>
        settings
      </Icon>
    </MDBox>
  );

  return (
    <ThemeProvider theme={themeDark}>
      <CssBaseline />
      {layout === 'dashboard' && auth.token && (
        <>
          <Sidenav
            color={sidenavColor}
            brand={brandWhite}
            brandName='RYVYL Dashboard'
            routes={routes}
            onMouseEnter={handleOnMouseEnter}
            onMouseLeave={handleOnMouseLeave}
          />
        </>
      )}
      <Routes>
        {memoizedRoutes}
        <Route
          path='*'
          element={<Navigate to={!localStorage.token ? '/dashboards/aml/transactions' : '/authentication/sign-in'} />}
        />
      </Routes>
    </ThemeProvider>
  );
}
